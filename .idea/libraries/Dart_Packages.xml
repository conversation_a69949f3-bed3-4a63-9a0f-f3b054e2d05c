<component name="libraryTable">
  <library name="Dart Packages" type="DartPackagesLibraryType">
    <properties>
      <option name="packageNameToDirsMap">
        <entry key="app_links">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/app_links-6.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="app_links_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/app_links_linux-1.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="app_links_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/app_links_platform_interface-2.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="app_links_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/app_links_web-1.0.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="archive">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/archive-4.0.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="args">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/args-2.7.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="async">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/async-2.13.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="boolean_selector">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="characters">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/characters-1.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="charcode">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/charcode-1.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="checked_yaml">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/checked_yaml-2.0.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="cli_util">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/cli_util-0.4.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="clock">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/clock-1.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="collection">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/collection-1.19.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="convert">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/convert-3.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="cross_file">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="crypto">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="csslib">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="cupertino_icons">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="equatable">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="fake_async">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/fake_async-1.3.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="ffi">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="file">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/file-7.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="file_selector_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="file_selector_macos">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/lib" />
            </list>
          </value>
        </entry>
        <entry key="file_selector_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="file_selector_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/lib" />
            </list>
          </value>
        </entry>
        <entry key="fl_chart">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/fl_chart-1.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../Softwares/flutter/packages/flutter/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_launcher_icons">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.14.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_plugin_android_lifecycle">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_staggered_grid_view">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_staggered_grid_view-0.7.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_svg">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_svg-2.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_test">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../Softwares/flutter/packages/flutter_test/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_web_plugins">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../Softwares/flutter/packages/flutter_web_plugins/lib" />
            </list>
          </value>
        </entry>
        <entry key="functions_client">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/functions_client-2.4.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_nav_bar">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/google_nav_bar-5.0.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="gotrue">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/gotrue-2.13.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="gtk">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/gtk-2.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="html">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/html-0.15.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="http">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/http-1.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="http_parser">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="image">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/image-4.5.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker-1.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+24/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_for_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_ios">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_macos">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="image_picker_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/lib" />
            </list>
          </value>
        </entry>
        <entry key="infinite_scroll_pagination">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/infinite_scroll_pagination-5.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="json_annotation">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="jwt_decode">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/jwt_decode-0.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="leak_tracker">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9/lib" />
            </list>
          </value>
        </entry>
        <entry key="leak_tracker_flutter_testing">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/lib" />
            </list>
          </value>
        </entry>
        <entry key="leak_tracker_testing">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="logging">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/logging-1.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="lucide_icons_flutter">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/lucide_icons_flutter-3.0.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="matcher">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/matcher-0.12.17/lib" />
            </list>
          </value>
        </entry>
        <entry key="material_color_utilities">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="meta">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/meta-1.16.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="mime">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/mime-2.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="path">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path-1.9.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_parsing">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path_parsing-1.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider-2.1.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_foundation">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="petitparser">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="platform">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/platform-3.1.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="plugin_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="posix">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/posix-6.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="postgrest">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/postgrest-2.4.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="realtime_client">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/realtime_client-2.5.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="responsive_framework">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/responsive_framework-1.5.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="retry">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/retry-3.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="rxdart">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_foundation">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="sky_engine">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../Softwares/flutter/bin/cache/pkg/sky_engine/lib" />
            </list>
          </value>
        </entry>
        <entry key="sliver_tools">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/sliver_tools-0.2.12/lib" />
            </list>
          </value>
        </entry>
        <entry key="source_span">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="stack_trace">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="storage_client">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/storage_client-2.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="stream_channel">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="string_scanner">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="supabase">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/supabase-2.8.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="supabase_flutter">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/supabase_flutter-2.9.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="term_glyph">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="test_api">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/test_api-0.7.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="typed_data">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="universal_html">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/universal_html-2.2.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="universal_io">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/universal_io-2.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher-6.3.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_ios">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_macos">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="vector_graphics">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/vector_graphics-1.1.19/lib" />
            </list>
          </value>
        </entry>
        <entry key="vector_graphics_codec">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.13/lib" />
            </list>
          </value>
        </entry>
        <entry key="vector_graphics_compiler">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.17/lib" />
            </list>
          </value>
        </entry>
        <entry key="vector_math">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="vm_service">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/vm_service-15.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/web-1.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="web_socket">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/web_socket-1.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="web_socket_channel">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="webview_flutter">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/webview_flutter-4.13.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="webview_flutter_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/webview_flutter_android-4.8.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="webview_flutter_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="webview_flutter_wkwebview">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="xdg_directories">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="xml">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/xml-6.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="yaml">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/yaml-3.1.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="yet_another_json_isolate">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/yet_another_json_isolate-2.1.0/lib" />
            </list>
          </value>
        </entry>
      </option>
    </properties>
    <CLASSES>
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/app_links-6.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/app_links_linux-1.0.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/app_links_platform_interface-2.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/app_links_web-1.0.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/archive-4.0.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/args-2.7.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/async-2.13.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/characters-1.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/charcode-1.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/checked_yaml-2.0.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/cli_util-0.4.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/clock-1.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/collection-1.19.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/convert-3.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/csslib-1.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/fake_async-1.3.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/file-7.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/fl_chart-1.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.14.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_staggered_grid_view-0.7.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_svg-2.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/functions_client-2.4.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/google_nav_bar-5.0.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/gotrue-2.13.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/gtk-2.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/html-0.15.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/http-1.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/image-4.5.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker-1.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+24/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/infinite_scroll_pagination-5.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/jwt_decode-0.3.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/logging-1.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/lucide_icons_flutter-3.0.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/matcher-0.12.17/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/meta-1.16.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/mime-2.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path-1.9.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_parsing-1.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider-2.1.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/platform-3.1.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/posix-6.0.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/postgrest-2.4.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/realtime_client-2.5.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/responsive_framework-1.5.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/retry-3.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/sliver_tools-0.2.12/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/storage_client-2.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/supabase-2.8.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/supabase_flutter-2.9.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/test_api-0.7.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/universal_html-2.2.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/universal_io-2.2.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher-6.3.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/vector_graphics-1.1.19/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.13/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.17/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/vm_service-15.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/web-1.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/web_socket-1.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/webview_flutter-4.13.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/webview_flutter_android-4.8.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/xml-6.5.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/yaml-3.1.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/yet_another_json_isolate-2.1.0/lib" />
      <root url="file://$PROJECT_DIR$/../../Softwares/flutter/bin/cache/pkg/sky_engine/lib" />
      <root url="file://$PROJECT_DIR$/../../Softwares/flutter/packages/flutter/lib" />
      <root url="file://$PROJECT_DIR$/../../Softwares/flutter/packages/flutter_test/lib" />
      <root url="file://$PROJECT_DIR$/../../Softwares/flutter/packages/flutter_web_plugins/lib" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>