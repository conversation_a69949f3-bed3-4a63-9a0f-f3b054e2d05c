import 'package:flutter/material.dart';
import '../services/trading_bot_service.dart';
import '../services/supabase_auth_service.dart';
import '../models/coin_model.dart';
import '../models/trading_bot.dart';

class DatabaseTestWidget extends StatefulWidget {
  const DatabaseTestWidget({super.key});

  @override
  State<DatabaseTestWidget> createState() => _DatabaseTestWidgetState();
}

class _DatabaseTestWidgetState extends State<DatabaseTestWidget> {
  String _testResults = '';
  bool _isRunning = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Database Test'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
      ),
      backgroundColor: Colors.black,
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ElevatedButton(
              onPressed: _isRunning ? null : _runDatabaseTests,
              child: Text(_isRunning ? 'Running Tests...' : 'Run Database Tests'),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[900],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _testResults.isEmpty ? 'Click "Run Database Tests" to start' : _testResults,
                    style: const TextStyle(
                      color: Colors.white,
                      fontFamily: 'monospace',
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _runDatabaseTests() async {
    setState(() {
      _isRunning = true;
      _testResults = 'Starting database tests...\n\n';
    });

    final service = TradingBotService();
    final authService = SupabaseAuthService();

    try {
      // Test 1: Check authentication
      _addResult('🔐 Testing authentication...');
      final userId = authService.userId;
      final isAuth = authService.isAuthenticated;
      _addResult('User ID: $userId');
      _addResult('Is Authenticated: $isAuth');
      
      if (userId == null) {
        _addResult('❌ User not authenticated - please sign in first');
        return;
      }
      _addResult('✅ Authentication successful\n');

      // Test 2: Database connection
      _addResult('🔗 Testing database connection...');
      final dbTest = await service.testDatabaseConnection();
      if (dbTest) {
        _addResult('✅ Database connection successful\n');
      } else {
        _addResult('❌ Database connection failed\n');
        return;
      }

      // Test 3: Create a test trading bot
      _addResult('🤖 Creating test trading bot...');
      
      final testCoin = CoinData(
        id: 'test-coin',
        symbol: 'TEST',
        name: 'Test Coin',
        image: null,
      );

      final testIndicators = [
        TradingIndicator(
          type: IndicatorType.rsi,
          parameters: {'period': 14},
          buyThreshold: 30.0,
          sellThreshold: 70.0,
        ),
      ];

      final result = await service.createTradingBot(
        name: 'Test Bot ${DateTime.now().millisecondsSinceEpoch}',
        coin: testCoin,
        strategy: 'DCA',
        investmentAmount: 100.0,
        stopLoss: 5.0,
        takeProfit: 10.0,
        indicators: testIndicators,
        startImmediately: false,
        riskPercentage: 2.0,
        trading24_7: true,
      );

      if (result != null) {
        _addResult('✅ Test trading bot created successfully!');
        _addResult('Bot ID: ${result['id']}');
        _addResult('Bot Name: ${result['name']}');
        _addResult('Investment: \$${result['investment_amount']}');
        _addResult('Strategy: ${result['strategy']}');
        _addResult('Indicators: ${result['indicators']}');
      } else {
        _addResult('❌ Failed to create test trading bot');
      }

      // Test 4: Retrieve trading bots
      _addResult('\n📋 Testing bot retrieval...');
      final bots = await service.getUserTradingBots();
      _addResult('Retrieved ${bots.length} trading bots');
      
      for (final bot in bots.take(3)) {
        _addResult('- ${bot['name']} (${bot['strategy']})');
      }

      _addResult('\n🎉 All tests completed successfully!');

    } catch (e, stackTrace) {
      _addResult('❌ Test failed with error: $e');
      _addResult('Stack trace: $stackTrace');
    } finally {
      setState(() => _isRunning = false);
    }
  }

  void _addResult(String message) {
    setState(() {
      _testResults += '$message\n';
    });
  }
}
