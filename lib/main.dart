import 'package:dextrip_app/homepage.dart';
import 'package:flutter/material.dart';
import 'theme/app_theme.dart';
import 'services/supabase_auth_service.dart';

import 'services/deep_link_service.dart';
import 'services/app_lifecycle_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize services
  await SupabaseAuthService.initialize();

  // Initialize deep link service (may fail on web, which is expected)
  try {
    await DeepLinkService.initialize();
  } catch (e) {
    debugPrint('Deep link service not available on this platform: $e');
  }

  runApp(const DexTrip());
}

class DexTrip extends StatefulWidget {
  const DexTrip({super.key});

  @override
  State<DexTrip> createState() => _DexTripState();
}

class _DexTripState extends State<DexTrip> with WidgetsBindingObserver {
  ThemeMode _themeMode = ThemeMode.dark;
  final AppLifecycleService _lifecycleService = AppLifecycleService();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _lifecycleService.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.resumed:
        _lifecycleService.setAppForegroundState(true);
        break;
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
      case AppLifecycleState.detached:
        _lifecycleService.setAppForegroundState(false);
        break;
      case AppLifecycleState.hidden:
        _lifecycleService.setAppForegroundState(false);
        break;
    }
  }

  void _toggleTheme() {
    setState(() {
      _themeMode = _themeMode == ThemeMode.dark
          ? ThemeMode.light
          : ThemeMode.dark;
    });
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      themeMode: _themeMode,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      home: MyHomePage(
        title: 'Dextrip',
        onThemeToggle: _toggleTheme,
        isDarkMode: _themeMode == ThemeMode.dark || _themeMode == ThemeMode.system,
      ),
    );
  }
}
