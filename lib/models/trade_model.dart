import 'coin_model.dart';

// Use CoinData instead of Coin to match the existing model

enum TradeStatus { active, watching, archived }

enum TradeSignal { buy, sell, hold }

enum TradeType { buy, sell }

class Trade {
  final String id;
  final CoinData coin;
  final String botName;
  final double amount;
  final double entryPrice;
  final double? currentPrice;
  final double? exitPrice;
  final DateTime entryTime;
  final DateTime? exitTime;
  final DateTime lastCheck;
  final TradeStatus status;
  final TradeSignal signal;
  final List<String> indicators;
  final double profitLoss;
  final double profitLossPercentage;
  final String duration;
  final bool isProfit;

  Trade({
    required this.id,
    required this.coin,
    required this.botName,
    required this.amount,
    required this.entryPrice,
    this.currentPrice,
    this.exitPrice,
    required this.entryTime,
    this.exitTime,
    required this.lastCheck,
    required this.status,
    required this.signal,
    required this.indicators,
    required this.profitLoss,
    required this.profitLossPercentage,
    required this.duration,
    required this.isProfit,
  });

  factory Trade.fromJson(Map<String, dynamic> json) {
    return Trade(
      id: json['id'],
      coin: CoinData.fromJson(json['coin']),
      botName: json['botName'],
      amount: json['amount'].toDouble(),
      entryPrice: json['entryPrice'].toDouble(),
      currentPrice: json['currentPrice']?.toDouble(),
      exitPrice: json['exitPrice']?.toDouble(),
      entryTime: DateTime.parse(json['entryTime']),
      exitTime: json['exitTime'] != null
          ? DateTime.parse(json['exitTime'])
          : null,
      lastCheck: DateTime.parse(json['lastCheck']),
      status: TradeStatus.values.firstWhere((e) => e.name == json['status']),
      signal: TradeSignal.values.firstWhere((e) => e.name == json['signal']),
      indicators: List<String>.from(json['indicators']),
      profitLoss: json['profitLoss'].toDouble(),
      profitLossPercentage: json['profitLossPercentage'].toDouble(),
      duration: json['duration'],
      isProfit: json['isProfit'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'coin': coin.toJson(),
      'botName': botName,
      'amount': amount,
      'entryPrice': entryPrice,
      'currentPrice': currentPrice,
      'exitPrice': exitPrice,
      'entryTime': entryTime.toIso8601String(),
      'exitTime': exitTime?.toIso8601String(),
      'lastCheck': lastCheck.toIso8601String(),
      'status': status.name,
      'signal': signal.name,
      'indicators': indicators,
      'profitLoss': profitLoss,
      'profitLossPercentage': profitLossPercentage,
      'duration': duration,
      'isProfit': isProfit,
    };
  }

  Trade copyWith({
    String? id,
    CoinData? coin,
    String? botName,
    double? amount,
    double? entryPrice,
    double? currentPrice,
    double? exitPrice,
    DateTime? entryTime,
    DateTime? exitTime,
    DateTime? lastCheck,
    TradeStatus? status,
    TradeSignal? signal,
    List<String>? indicators,
    double? profitLoss,
    double? profitLossPercentage,
    String? duration,
    bool? isProfit,
  }) {
    return Trade(
      id: id ?? this.id,
      coin: coin ?? this.coin,
      botName: botName ?? this.botName,
      amount: amount ?? this.amount,
      entryPrice: entryPrice ?? this.entryPrice,
      currentPrice: currentPrice ?? this.currentPrice,
      exitPrice: exitPrice ?? this.exitPrice,
      entryTime: entryTime ?? this.entryTime,
      exitTime: exitTime ?? this.exitTime,
      lastCheck: lastCheck ?? this.lastCheck,
      status: status ?? this.status,
      signal: signal ?? this.signal,
      indicators: indicators ?? this.indicators,
      profitLoss: profitLoss ?? this.profitLoss,
      profitLossPercentage: profitLossPercentage ?? this.profitLossPercentage,
      duration: duration ?? this.duration,
      isProfit: isProfit ?? this.isProfit,
    );
  }
}
