import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../widgets/bot_config_modal.dart';
import '../models/trading_bot_model.dart';
import '../services/app_lifecycle_service.dart';

class BotPage extends StatefulWidget {
  const BotPage({super.key});

  @override
  State<BotPage> createState() => _BotPageState();
}

enum BotFilter { all, active, paused, archived }

class _BotPageState extends State<BotPage> with TickerProviderStateMixin {
  List<TradingBot> _bots = [];
  Timer? _priceUpdateTimer;
  Timer? _tradeTimer;
  final AppLifecycleService _lifecycleService = AppLifecycleService();
  StreamSubscription<AppPage>? _pageChangeSubscription;
  final Random _random = Random();
  String _selectedCategory = 'Active';

  @override
  void initState() {
    super.initState();
    _loadDummyBots();
    _setupLifecycleListener();

    // Only start updates if on bots page
    if (_lifecycleService.shouldEnableBotUpdates) {
      _startPriceUpdates();
      _startTradeGeneration();
    }
  }

  @override
  void dispose() {
    _priceUpdateTimer?.cancel();
    _tradeTimer?.cancel();
    _pageChangeSubscription?.cancel();
    super.dispose();
  }

  void _setupLifecycleListener() {
    _pageChangeSubscription = _lifecycleService.pageChangeStream.listen((page) {
      if (page == AppPage.bots) {
        // Start updates when on bots page
        if (_priceUpdateTimer == null) {
          _startPriceUpdates();
        }
        if (_tradeTimer == null) {
          _startTradeGeneration();
        }
      } else {
        // Stop updates when not on bots page
        _stopUpdates();
      }
    });
  }

  void _loadDummyBots() {
    setState(() {
      _bots = [
        TradingBot(
          id: '1',
          name: 'BONK Scalper',
          tokenSymbol: 'BONK',
          tokenImage: 'assets/images/bonk.png',
          currentPrice: 0.000012,
          priceChange24h: 15.6,
          isActive: true,
          strategy: 'Scalping',
          solPerTrade: 0.1,
          totalTrades: 47,
          winRate: 68.1,
          totalEarned: 2.34,
          trades: [],
        ),
        TradingBot(
          id: '2',
          name: 'PEPE Moon Bot',
          tokenSymbol: 'PEPE',
          tokenImage: 'assets/images/pepe.png',
          currentPrice: 0.00000089,
          priceChange24h: -8.3,
          isActive: false,
          strategy: 'Momentum',
          solPerTrade: 0.2,
          totalTrades: 23,
          winRate: 52.2,
          totalEarned: 1.12,
          trades: [],
        ),
        TradingBot(
          id: '3',
          name: 'WIF Swing Trader',
          tokenSymbol: 'WIF',
          tokenImage: 'assets/images/wif.png',
          currentPrice: 2.45,
          priceChange24h: 23.7,
          isActive: true,
          strategy: 'Swing Trading',
          solPerTrade: 0.5,
          totalTrades: 12,
          winRate: 83.3,
          totalEarned: 4.67,
          trades: [],
        ),
      ];
    });
  }

  void _startPriceUpdates() {
    if (_priceUpdateTimer != null) return; // Already running

    _priceUpdateTimer = Timer.periodic(const Duration(seconds: 2), (timer) {
      if (mounted && _lifecycleService.shouldEnableBotUpdates) {
        setState(() {
          for (var bot in _bots) {
            // Simulate price changes (-5% to +5%)
            double changePercent = (_random.nextDouble() - 0.5) * 0.1;
            bot.currentPrice *= (1 + changePercent);

            // Update 24h change
            bot.priceChange24h += (_random.nextDouble() - 0.5) * 2;
            bot.priceChange24h = bot.priceChange24h.clamp(-50.0, 50.0);
          }
        });
      }
    });
  }

  void _stopUpdates() {
    _priceUpdateTimer?.cancel();
    _priceUpdateTimer = null;
    _tradeTimer?.cancel();
    _tradeTimer = null;
  }

  void _startTradeGeneration() {
    if (_tradeTimer != null) return; // Already running

    _tradeTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (mounted && _lifecycleService.shouldEnableBotUpdates) {
        for (var bot in _bots) {
          if (bot.isActive && _random.nextDouble() < 0.3) {
            _generateTrade(bot);
          }
        }
      }
    });
  }

  void _generateTrade(TradingBot bot) {
    final isWin = _random.nextDouble() < (bot.winRate / 100);
    final tradeType = _random.nextBool() ? 'BUY' : 'SELL';
    final profit = isWin
        ? bot.solPerTrade *
              (_random.nextDouble() * 0.2 + 0.05) // 5-25% profit
        : -bot.solPerTrade * (_random.nextDouble() * 0.15 + 0.02); // 2-17% loss

    final trade = Trade(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: tradeType,
      price: bot.currentPrice,
      amount: bot.solPerTrade,
      profit: profit,
      timestamp: DateTime.now(),
      status: 'SUCCESS',
    );

    setState(() {
      bot.trades.insert(0, trade);
      if (bot.trades.length > 20) {
        bot.trades.removeLast();
      }
      bot.totalTrades++;
      bot.totalEarned += profit;

      // Recalculate win rate
      final wins = bot.trades.where((t) => t.profit > 0).length;
      bot.winRate = (wins / bot.trades.length) * 100;
    });
  }

  // void _createNewBot() {
  //   _showBotTypeSelection();
  // }

  // void _showBotTypeSelection() {
  //   showModalBottomSheet(
  //     context: context,
  //     isScrollControlled: true,
  //     backgroundColor: Colors.transparent,
  //     builder: (context) => Container(
  //       padding: const EdgeInsets.all(24),
  //       decoration: const BoxDecoration(
  //         color: Color(0xFF1A1A1A),
  //         borderRadius: BorderRadius.only(
  //           topLeft: Radius.circular(20),
  //           topRight: Radius.circular(20),
  //         ),
  //       ),
  //       child: Column(
  //         mainAxisSize: MainAxisSize.min,
  //         children: [
  //           // Handle bar
  //           Container(
  //             margin: const EdgeInsets.only(bottom: 20),
  //             width: 40,
  //             height: 4,
  //             decoration: BoxDecoration(
  //               color: Colors.white24,
  //               borderRadius: BorderRadius.circular(2),
  //             ),
  //           ),

  //           const Text(
  //             'Create New Bot',
  //             style: TextStyle(
  //               fontSize: 24,
  //               fontWeight: FontWeight.bold,
  //               color: Colors.white,
  //             ),
  //           ),

  //           // const SizedBox(height: 8),

  //           // Text(
  //           //   'Choose the type of trading bot you want to create',
  //           //   style: TextStyle(
  //           //     fontSize: 16,
  //           //     color: Colors.white.withValues(alpha: 0.7),
  //           //   ),
  //           //   textAlign: TextAlign.center,
  //           // ),
  //           const SizedBox(height: 32),

  //           // AI Bot Sniper Option
  //           _buildBotTypeOption(
  //             title: 'AI Bot Sniper',
  //             subtitle:
  //                 'Let AI analyze market trends and find profitable opportunities',
  //             icon: LucideIcons.brain,
  //             gradient: [Colors.purple, Colors.blue],
  //             onTap: () {
  //               Navigator.pop(context);
  //               _showAIBotSelection();
  //             },
  //           ),

  //           const SizedBox(height: 16),

  //           // Single Coin Bot Option
  //           _buildBotTypeOption(
  //             title: 'Single Coin Bot',
  //             subtitle: 'Focus on trading a specific cryptocurrency',
  //             icon: LucideIcons.target,
  //             gradient: [Colors.green, Colors.teal],
  //             onTap: () {
  //               Navigator.pop(context);
  //               _showCoinSelection();
  //             },
  //           ),

  //           const SizedBox(height: 24),
  //         ],
  //       ),
  //     ),
  //   );
  // }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        title: Row(
          children: [
            Text('BOTS ', style: TextStyle(fontWeight: FontWeight.bold)),
            Spacer(),
            GestureDetector(
              onTap: _showCategoryModal,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: _getCategoryColor().withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: _getCategoryColor().withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: _getCategoryColor(),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      _selectedCategory.toUpperCase(),
                      style: TextStyle(
                        color: _getCategoryColor(),
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Icon(
                      LucideIcons.chevronDown,
                      color: _getCategoryColor(),
                      size: 16,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        centerTitle: false,
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),

      body: Column(
        children: [
          // Body content
          Expanded(child: _buildBotsList()),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.purple, Colors.blue],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(60),
            ),
            child: const Icon(LucideIcons.bot, size: 60, color: Colors.white),
          ),
          const SizedBox(height: 24),
          const Text(
            'No Trading Bots Yet',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'Create your first trading bot to start\nautomated trading with custom indicators',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              color: Colors.white.withValues(alpha: 0.7),
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  List<TradingBot> _getFilteredBots() {
    switch (_selectedCategory) {
      case 'All':
        return _bots.where((bot) => !bot.isArchived).toList();
      case 'Active':
        return _bots.where((bot) => bot.isActive && !bot.isArchived).toList();
      case 'Paused':
        return _bots.where((bot) => !bot.isActive && !bot.isArchived).toList();
      case 'Archive':
        return _bots.where((bot) => bot.isArchived).toList();
      default:
        return _bots.where((bot) => bot.isActive && !bot.isArchived).toList();
    }
  }

  Widget _buildBotsList() {
    final filteredBots = _getFilteredBots();

    if (filteredBots.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              LucideIcons.bot,
              size: 64,
              color: Colors.white.withValues(alpha: 0.3),
            ),
            const SizedBox(height: 16),
            Text(
              _getEmptyStateMessage(),
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.6),
                fontSize: 16,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: filteredBots.length,
      itemBuilder: (context, index) {
        final bot = filteredBots[index];
        return _buildBotCard(bot);
      },
    );
  }

  String _getEmptyStateMessage() {
    switch (_selectedCategory) {
      case 'All':
        return 'No bots created yet';
      case 'Active':
        return 'No active bots';
      case 'Paused':
        return 'No paused bots';
      case 'Archive':
        return 'No archived bots';
      default:
        return 'No bots found';
    }
  }

  Widget _buildBotCard(TradingBot bot) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.white.withValues(alpha: 0.03)
            : Colors.grey.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.white.withValues(alpha: 0.08)
              : Colors.grey.withValues(alpha: 0.2),
        ),
      ),
      child: InkWell(
        onTap: () => _showTradeHistory(bot),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Header row
              Row(
                children: [
                  // Token icon - smaller and minimal
                  Container(
                    width: 36,
                    height: 36,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.purple.withValues(alpha: 0.8),
                          Colors.blue.withValues(alpha: 0.8),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(18),
                    ),
                    child: Center(
                      child: Text(
                        bot.tokenSymbol.substring(0, 2),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(width: 12),

                  // Bot info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          bot.name,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Theme.of(context).textTheme.bodyLarge?.color,
                          ),
                        ),
                        Text(
                          bot.strategy,
                          style: TextStyle(
                            fontSize: 12,
                            color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.6),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Status and price
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 3,
                        ),
                        decoration: BoxDecoration(
                          color: bot.isActive
                              ? Colors.green.withValues(alpha: 0.15)
                              : Colors.orange.withValues(alpha: 0.15),
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(
                            color: bot.isActive
                                ? Colors.green.withValues(alpha: 0.3)
                                : Colors.orange.withValues(alpha: 0.3),
                            width: 0.5,
                          ),
                        ),
                        child: Text(
                          bot.isActive ? 'ACTIVE' : 'PAUSED',
                          style: TextStyle(
                            color: bot.isActive ? Colors.green : Colors.orange,
                            fontSize: 9,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '\$${bot.currentPrice.toStringAsFixed(bot.currentPrice < 0.01 ? 6 : 3)}',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Theme.of(context).textTheme.bodyLarge?.color,
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Stats row - more compact
              Row(
                children: [
                  Expanded(
                    child: _buildMinimalStat('${bot.totalTrades}', 'Trades'),
                  ),
                  Expanded(
                    child: _buildMinimalStat(
                      '${bot.winRate.toStringAsFixed(0)}%',
                      'Win Rate',
                    ),
                  ),
                  Expanded(
                    child: _buildMinimalStat(
                      '${bot.totalEarned.toStringAsFixed(2)}',
                      'SOL',
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Control buttons - minimal
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: bot.isActive
                            ? Colors.red.withValues(alpha: 0.15)
                            : Colors.green.withValues(alpha: 0.15),
                        foregroundColor: bot.isActive
                            ? Colors.red
                            : Colors.green,
                        elevation: 0,
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                          side: BorderSide(
                            color: bot.isActive
                                ? Colors.red.withValues(alpha: 0.3)
                                : Colors.green.withValues(alpha: 0.3),
                            width: 0.5,
                          ),
                        ),
                      ),
                      onPressed: () {
                        setState(() {
                          bot.isActive = !bot.isActive;
                        });
                      },
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            bot.isActive ? LucideIcons.pause : LucideIcons.play,
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            bot.isActive ? 'STOP BOT' : 'START BOT',
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 13,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.white.withValues(alpha: 0.05)
                          : Colors.grey.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.white.withValues(alpha: 0.1)
                            : Colors.grey.withValues(alpha: 0.3),
                        width: 0.5,
                      ),
                    ),
                    child: IconButton(
                      onPressed: () => _showTradeHistory(bot),
                      icon: Icon(
                        LucideIcons.arrowUpDown,
                        color: Theme.of(context).textTheme.bodyMedium?.color,
                        size: 16,
                      ),
                      padding: const EdgeInsets.all(10),
                      constraints: const BoxConstraints(),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.white.withValues(alpha: 0.05)
                          : Colors.grey.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.white.withValues(alpha: 0.1)
                            : Colors.grey.withValues(alpha: 0.3),
                        width: 0.5,
                      ),
                    ),
                    child: IconButton(
                      onPressed: () => _showBotConfig(bot),
                      icon: Icon(
                        LucideIcons.bolt,
                        color: Theme.of(context).textTheme.bodyMedium?.color,
                        size: 16,
                      ),
                      padding: const EdgeInsets.all(10),
                      constraints: const BoxConstraints(),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.05),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Colors.red.withValues(alpha: 0.1),
                        width: 0.5,
                      ),
                    ),
                    child: IconButton(
                      onPressed: () => _showDeleteBotDialog(bot),
                      icon: const Icon(
                        LucideIcons.trash2,
                        color: Colors.red,
                        size: 16,
                      ),
                      padding: const EdgeInsets.all(10),
                      constraints: const BoxConstraints(),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMinimalStat(String value, String label) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Theme.of(context).textTheme.bodyLarge?.color,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          label,
          style: TextStyle(
            fontSize: 11,
            color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.6),
          ),
        ),
      ],
    );
  }

  void _showTradeHistory(TradingBot bot) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          decoration: BoxDecoration(
            color: Theme.of(context).brightness == Brightness.dark
                ? const Color(0xFF1A1A1A)
                : Colors.white,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.only(top: 12, bottom: 20),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.white24
                      : Colors.grey.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Row(
                  children: [
                    Text(
                      '${bot.name} Trades',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).textTheme.bodyLarge?.color,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      '${bot.trades.length} trades',
                      style: TextStyle(
                        fontSize: 14,
                        color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 20),

              // Trade list
              Expanded(
                child: bot.trades.isNotEmpty
                    ? ListView.builder(
                        controller: scrollController,
                        padding: const EdgeInsets.symmetric(horizontal: 20),
                        itemCount: bot.trades.length,
                        itemBuilder: (context, index) {
                          final trade = bot.trades[index];
                          return _buildTradeItem(trade);
                        },
                      )
                    : Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              LucideIcons.activity,
                              color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.3),
                              size: 48,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'No trades yet',
                              style: TextStyle(
                                color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Start the bot to begin trading',
                              style: TextStyle(
                                color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.5),
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showBotConfig(TradingBot bot) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => BotConfigModal(bot: bot),
    );
  }

  void _showDeleteBotDialog(TradingBot bot) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1A1A1A),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(LucideIcons.trash2, color: Colors.red, size: 24),
            const SizedBox(width: 12),
            Text(
              'Delete Bot',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Are you sure you want to delete "${bot.name}"?',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.8),
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(LucideIcons.info, color: Colors.orange, size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'This action cannot be undone. All trade history will be lost.',
                      style: TextStyle(color: Colors.orange, fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: TextStyle(color: Colors.white.withValues(alpha: 0.6)),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _archiveBot(bot);
            },
            child: Text('Archive', style: TextStyle(color: Colors.orange)),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteBot(bot);
            },
            child: Text(
              'Delete',
              style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  void _archiveBot(TradingBot bot) {
    setState(() {
      bot.isArchived = true;
      bot.isActive = false;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${bot.name} archived successfully'),
        backgroundColor: Colors.orange,
        behavior: SnackBarBehavior.floating,
        action: SnackBarAction(
          label: 'Undo',
          textColor: Colors.white,
          onPressed: () {
            setState(() {
              bot.isArchived = false;
            });
          },
        ),
      ),
    );
  }

  void _deleteBot(TradingBot bot) {
    setState(() {
      _bots.remove(bot);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${bot.name} deleted successfully'),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  Color _getCategoryColor() {
    switch (_selectedCategory) {
      case 'Active':
        return Colors.green;
      case 'Paused':
        return Colors.orange;
      case 'Archive':
        return Colors.grey;
      default:
        return Colors.blue;
    }
  }

  void _showCategoryModal() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        decoration: const BoxDecoration(
          color: Color(0xFF1A1A1A),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(bottom: 20),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.white24,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            const Text(
              'Select Bot Category',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),

            const SizedBox(height: 24),

            // Category options
            ...[
              'Active',
              'Paused',
              'All',
              'Archive',
            ].map((category) => _buildCategoryOption(category)).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryOption(String category) {
    final isSelected = _selectedCategory == category;
    final color = _getCategoryColorForCategory(category);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedCategory = category;
          });
          Navigator.pop(context);
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isSelected
                ? color.withValues(alpha: 0.2)
                : Colors.white.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected
                  ? color.withValues(alpha: 0.3)
                  : Colors.white.withValues(alpha: 0.1),
            ),
          ),
          child: Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(_getCategoryIcon(category), color: color, size: 16),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      category,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      _getCategoryDescription(category),
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.white.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
              if (isSelected) Icon(LucideIcons.check, color: color, size: 20),
            ],
          ),
        ),
      ),
    );
  }

  Color _getCategoryColorForCategory(String category) {
    switch (category) {
      case 'Active':
        return Colors.green;
      case 'Paused':
        return Colors.orange;
      case 'Archive':
        return Colors.grey;
      default:
        return Colors.blue;
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'All':
        return LucideIcons.list;
      case 'Active':
        return LucideIcons.activity;
      case 'Paused':
        return LucideIcons.pause;
      case 'Archive':
        return LucideIcons.archive;
      default:
        return LucideIcons.list;
    }
  }

  String _getCategoryDescription(String category) {
    switch (category) {
      case 'All':
        return 'View all bots across categories';
      case 'Active':
        return 'Running bots with live trading';
      case 'Paused':
        return 'Stopped bots ready to resume';
      case 'Archive':
        return 'Archived and disabled bots';
      default:
        return '';
    }
  }

  Widget _buildTradeItem(Trade trade) {
    final isProfit = trade.profit > 0;
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.white.withValues(alpha: 0.1)
                : Colors.grey.withValues(alpha: 0.2),
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          // Trade type indicator
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: trade.type == 'BUY' ? Colors.green : Colors.red,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Icon(
              trade.type == 'BUY'
                  ? LucideIcons.trendingUp
                  : LucideIcons.trendingDown,
              color: Colors.white,
              size: 16,
            ),
          ),

          const SizedBox(width: 12),

          // Trade details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      trade.type,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).textTheme.bodyLarge?.color,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '\$${trade.price.toStringAsFixed(trade.price < 0.01 ? 8 : 4)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 2),
                Text(
                  '${trade.amount.toStringAsFixed(2)} SOL',
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.5),
                  ),
                ),
              ],
            ),
          ),

          // Profit/Loss
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '${isProfit ? '+' : ''}${trade.profit.toStringAsFixed(3)} SOL',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: isProfit ? Colors.green : Colors.red,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                _formatTime(trade.timestamp),
                style: TextStyle(
                  fontSize: 11,
                  color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.5),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  Widget _buildConfigSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.03),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.white.withValues(alpha: 0.08)),
          ),
          child: Column(children: children),
        ),
      ],
    );
  }

  Widget _buildConfigToggle(String label, bool value, String description) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Colors.white.withValues(alpha: 0.05),
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: value
                  ? Colors.green.withValues(alpha: 0.2)
                  : Colors.grey.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              value ? LucideIcons.check : LucideIcons.x,
              color: value ? Colors.green : Colors.grey,
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.white.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: (newValue) {
              // TODO: Implement toggle functionality
            },
            activeColor: Colors.green,
            inactiveThumbColor: Colors.grey,
            inactiveTrackColor: Colors.grey.withValues(alpha: 0.3),
          ),
        ],
      ),
    );
  }

  Widget _buildBotTypeOption({
    required String title,
    required String subtitle,
    required IconData icon,
    required List<Color> gradient,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: gradient.map((c) => c.withValues(alpha: 0.1)).toList(),
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: gradient.first.withValues(alpha: 0.3)),
        ),
        child: Row(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: gradient,
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(30),
              ),
              child: Icon(icon, color: Colors.white, size: 28),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.white.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              LucideIcons.chevronRight,
              color: Colors.white.withValues(alpha: 0.5),
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  void _showAIBotSelection() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.8,
        maxChildSize: 0.9,
        minChildSize: 0.6,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Color(0xFF1A1A1A),
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.only(top: 12, bottom: 20),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.white24,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Row(
                  children: [
                    const Text(
                      'AI Bot Agents',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(
                        LucideIcons.x,
                        color: Colors.white,
                        size: 20,
                      ),
                      padding: const EdgeInsets.all(8),
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 20),

              // AI Agents List
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    children: [
                      // Create Custom AI Agent
                      _buildAIAgentOption(
                        title: 'Create Custom AI Agent',
                        subtitle:
                            'Describe your trading strategy and let AI find coins',
                        icon: LucideIcons.plus,
                        gradient: [Colors.purple, Colors.pink],
                        isCustom: true,
                        onTap: () {
                          Navigator.pop(context);
                          _showCustomAIPrompt();
                        },
                      ),

                      const SizedBox(height: 16),

                      // Default AI Agents
                      _buildAIAgentOption(
                        title: 'Meme Coin Hunter',
                        subtitle:
                            'Specializes in finding trending meme coins with high potential',
                        icon: LucideIcons.trendingUp,
                        gradient: [Colors.orange, Colors.red],
                        description:
                            'Analyzes social sentiment, volume spikes, and community growth',
                        onTap: () => _selectAIAgent('meme_hunter'),
                      ),

                      const SizedBox(height: 12),

                      _buildAIAgentOption(
                        title: 'DeFi Yield Optimizer',
                        subtitle:
                            'Finds high-yield DeFi opportunities with optimal risk/reward',
                        icon: LucideIcons.coins,
                        gradient: [Colors.green, Colors.teal],
                        description:
                            'Monitors liquidity pools, farming rewards, and protocol health',
                        onTap: () => _selectAIAgent('defi_optimizer'),
                      ),

                      const SizedBox(height: 12),

                      _buildAIAgentOption(
                        title: 'Breakout Detector',
                        subtitle:
                            'Identifies coins about to break resistance levels',
                        icon: LucideIcons.activity,
                        gradient: [Colors.blue, Colors.cyan],
                        description:
                            'Uses technical analysis and volume patterns',
                        onTap: () => _selectAIAgent('breakout_detector'),
                      ),

                      const SizedBox(height: 12),

                      _buildAIAgentOption(
                        title: 'News Sentiment Trader',
                        subtitle:
                            'Trades based on news sentiment and market reactions',
                        icon: LucideIcons.newspaper,
                        gradient: [Colors.indigo, Colors.purple],
                        description:
                            'Analyzes news, social media, and market sentiment',
                        onTap: () => _selectAIAgent('news_sentiment'),
                      ),

                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showCoinSelection() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.8,
        maxChildSize: 0.9,
        minChildSize: 0.6,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Color(0xFF1A1A1A),
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.only(top: 12, bottom: 20),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.white24,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Row(
                  children: [
                    const Text(
                      'Select Coin',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(
                        LucideIcons.x,
                        color: Colors.white,
                        size: 20,
                      ),
                      padding: const EdgeInsets.all(8),
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),
              ),

              // Search Bar
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.1),
                    ),
                  ),
                  child: TextField(
                    style: const TextStyle(color: Colors.white),
                    decoration: InputDecoration(
                      hintText: 'Search coin name or token symbol...',
                      hintStyle: TextStyle(
                        color: Colors.white.withValues(alpha: 0.5),
                      ),
                      prefixIcon: Icon(
                        LucideIcons.search,
                        color: Colors.white.withValues(alpha: 0.5),
                      ),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.all(16),
                    ),
                    onChanged: (value) {
                      // TODO: Implement search functionality
                    },
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // Popular Coins
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Popular Coins',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Popular coin options
                      ..._getPopularCoins()
                          .map((coin) => _buildCoinOption(coin))
                          .toList(),

                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAIAgentOption({
    required String title,
    required String subtitle,
    required IconData icon,
    required List<Color> gradient,
    required VoidCallback onTap,
    String? description,
    bool isCustom = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.03),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: isCustom
                  ? gradient.first.withValues(alpha: 0.5)
                  : Colors.white.withValues(alpha: 0.1),
              width: isCustom ? 1.5 : 1,
            ),
          ),
          child: Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: gradient,
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Icon(icon, color: Colors.white, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white.withValues(alpha: 0.7),
                      ),
                    ),
                    if (description != null) ...[
                      const SizedBox(height: 8),
                      Text(
                        description,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.white.withValues(alpha: 0.5),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              Icon(
                LucideIcons.chevronRight,
                color: Colors.white.withValues(alpha: 0.3),
                size: 20,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showCustomAIPrompt() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1A1A1A),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Colors.purple, Colors.pink],
                ),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                LucideIcons.brain,
                color: Colors.white,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            const Text(
              'Custom AI Agent',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Describe your trading strategy and the AI will suggest coins that match your criteria.',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.8),
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
              ),
              child: TextField(
                maxLines: 4,
                style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  hintText:
                      'e.g., "Find meme coins with strong community, recent partnerships, and volume spikes over 200%"',
                  hintStyle: TextStyle(
                    color: Colors.white.withValues(alpha: 0.5),
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.all(16),
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.7),
                fontSize: 16,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _showIndicatorSelection('custom_ai', null);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.purple,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'Create Agent',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }

  void _selectAIAgent(String agentType) {
    Navigator.pop(context);
    _showIndicatorSelection('ai_agent', agentType);
  }

  List<Map<String, dynamic>> _getPopularCoins() {
    return [
      {
        'name': 'Solana',
        'symbol': 'SOL',
        'price': 102.45,
        'change': 5.2,
        'icon': '◎',
      },
      {
        'name': 'Bonk',
        'symbol': 'BONK',
        'price': 0.000012,
        'change': 15.6,
        'icon': '🐕',
      },
      {
        'name': 'Dogwifhat',
        'symbol': 'WIF',
        'price': 2.45,
        'change': -3.1,
        'icon': '🐶',
      },
      {
        'name': 'Pepe',
        'symbol': 'PEPE',
        'price': 0.00000089,
        'change': 8.7,
        'icon': '🐸',
      },
      {
        'name': 'Jupiter',
        'symbol': 'JUP',
        'price': 0.85,
        'change': -2.4,
        'icon': '🪐',
      },
      {
        'name': 'Raydium',
        'symbol': 'RAY',
        'price': 1.20,
        'change': 12.3,
        'icon': '⚡',
      },
    ];
  }

  Widget _buildCoinOption(Map<String, dynamic> coin) {
    final isPositive = coin['change'] > 0;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () {
          Navigator.pop(context);
          _showIndicatorSelection('single_coin', coin['symbol']);
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.03),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.white.withValues(alpha: 0.08)),
          ),
          child: Row(
            children: [
              Container(
                width: 44,
                height: 44,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(22),
                ),
                child: Center(
                  child: Text(
                    coin['icon'],
                    style: const TextStyle(fontSize: 20),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      coin['name'],
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      coin['symbol'],
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '\$${coin['price'].toStringAsFixed(coin['price'] < 0.01 ? 8 : 2)}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                  Text(
                    '${isPositive ? '+' : ''}${coin['change'].toStringAsFixed(1)}%',
                    style: TextStyle(
                      fontSize: 14,
                      color: isPositive ? Colors.green : Colors.red,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showIndicatorSelection(String botType, String? selection) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.8,
        maxChildSize: 0.9,
        minChildSize: 0.6,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Color(0xFF1A1A1A),
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.only(top: 12, bottom: 20),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.white24,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Row(
                  children: [
                    const Text(
                      'Select Indicators',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(
                        LucideIcons.x,
                        color: Colors.white,
                        size: 20,
                      ),
                      padding: const EdgeInsets.all(8),
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),
              ),

              // Subtitle
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Text(
                  'Choose technical indicators for your trading bot',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white.withValues(alpha: 0.7),
                  ),
                ),
              ),

              const SizedBox(height: 20),

              // Indicators List
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    children: [
                      _buildIndicatorToggle(
                        'RSI (14)',
                        'Relative Strength Index',
                        true,
                      ),
                      _buildIndicatorToggle(
                        'MACD',
                        'Moving Average Convergence Divergence',
                        true,
                      ),
                      _buildIndicatorToggle(
                        'Bollinger Bands',
                        'Price volatility indicator',
                        false,
                      ),
                      _buildIndicatorToggle(
                        'EMA (20)',
                        'Exponential Moving Average',
                        true,
                      ),
                      _buildIndicatorToggle(
                        'Volume Profile',
                        'Trading volume analysis',
                        false,
                      ),
                      _buildIndicatorToggle(
                        'Stochastic RSI',
                        'Momentum oscillator',
                        false,
                      ),
                      _buildIndicatorToggle(
                        'Williams %R',
                        'Momentum indicator',
                        false,
                      ),
                      _buildIndicatorToggle(
                        'Fibonacci Retracement',
                        'Support and resistance levels',
                        false,
                      ),

                      const SizedBox(height: 32),

                      // Continue button
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.pop(context);
                            _showBotConfiguration(botType, selection);
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text(
                            'Continue to Configuration',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildIndicatorToggle(
    String name,
    String description,
    bool defaultValue,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.03),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.08)),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: defaultValue
                  ? Colors.green.withValues(alpha: 0.2)
                  : Colors.grey.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              defaultValue ? LucideIcons.check : LucideIcons.x,
              color: defaultValue ? Colors.green : Colors.grey,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: defaultValue,
            onChanged: (value) {
              // TODO: Handle indicator toggle
            },
            activeColor: Colors.green,
            inactiveThumbColor: Colors.grey,
            inactiveTrackColor: Colors.grey.withValues(alpha: 0.3),
          ),
        ],
      ),
    );
  }

  void _showBotConfiguration(String botType, String? selection) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.9,
        maxChildSize: 0.95,
        minChildSize: 0.7,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Color(0xFF1A1A1A),
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.only(top: 12, bottom: 20),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.white24,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Row(
                  children: [
                    const Text(
                      'Bot Configuration',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(
                        LucideIcons.x,
                        color: Colors.white,
                        size: 20,
                      ),
                      padding: const EdgeInsets.all(8),
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),
              ),

              // Configuration content
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Bot Name
                      _buildConfigField(
                        'Bot Name',
                        'Enter a name for your bot',
                        _getBotName(botType, selection),
                      ),

                      const SizedBox(height: 24),

                      // Trading Strategy
                      _buildConfigSection('Trading Strategy', [
                        _buildConfigSlider(
                          'SOL per Trade',
                          0.1,
                          0.01,
                          2.0,
                          'SOL',
                        ),
                        _buildConfigSlider(
                          'Max Daily Trades',
                          50,
                          1,
                          100,
                          'trades',
                        ),
                        _buildConfigDropdown('Strategy Type', 'Scalping', [
                          'Scalping',
                          'Swing Trading',
                          'DCA',
                        ]),
                      ]),

                      const SizedBox(height: 24),

                      // Risk Management
                      _buildConfigSection('Risk Management', [
                        _buildConfigSlider('Stop Loss', 5.0, 1.0, 20.0, '%'),
                        _buildConfigSlider('Take Profit', 15.0, 5.0, 50.0, '%'),
                        _buildConfigSlider(
                          'Max Drawdown',
                          20.0,
                          5.0,
                          50.0,
                          '%',
                        ),
                      ]),

                      const SizedBox(height: 24),

                      // Trading Hours
                      _buildConfigSection('Trading Hours', [
                        _buildConfigToggle(
                          '24/7 Trading',
                          true,
                          'Trade around the clock',
                        ),
                        _buildConfigDropdown('Timezone', 'UTC', [
                          'UTC',
                          'EST',
                          'PST',
                          'GMT',
                        ]),
                      ]),

                      const SizedBox(height: 32),

                      // Create Bot button
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () => _createBot(botType, selection),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text(
                            'Create Bot',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getBotName(String botType, String? selection) {
    if (botType == 'ai_agent') {
      switch (selection) {
        case 'meme_hunter':
          return 'Meme Hunter Bot';
        case 'defi_optimizer':
          return 'DeFi Optimizer Bot';
        case 'breakout_detector':
          return 'Breakout Detector Bot';
        case 'news_sentiment':
          return 'News Sentiment Bot';
        default:
          return 'Custom AI Bot';
      }
    } else {
      return '$selection Trading Bot';
    }
  }

  Widget _buildConfigField(String label, String hint, String defaultValue) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
          ),
          child: TextField(
            style: const TextStyle(color: Colors.white),
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: TextStyle(color: Colors.white.withValues(alpha: 0.5)),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.all(16),
            ),
            controller: TextEditingController(text: defaultValue),
          ),
        ),
      ],
    );
  }

  Widget _buildConfigSlider(
    String label,
    double value,
    double min,
    double max,
    String unit,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Colors.white.withValues(alpha: 0.05),
            width: 0.5,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.white,
                ),
              ),
              Text(
                '${value.toStringAsFixed(value < 1 ? 2 : 0)} $unit',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Slider(
            value: value,
            min: min,
            max: max,
            activeColor: Colors.blue,
            inactiveColor: Colors.grey.withValues(alpha: 0.3),
            onChanged: (newValue) {
              // TODO: Handle slider change
            },
          ),
        ],
      ),
    );
  }

  Widget _buildConfigDropdown(
    String label,
    String value,
    List<String> options,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Colors.white.withValues(alpha: 0.05),
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.white,
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              value,
              style: const TextStyle(fontSize: 14, color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  void _createBot(String botType, String? selection) {
    // Create new bot and add to list
    final newBot = TradingBot(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: _getBotName(botType, selection),
      tokenSymbol: selection ?? 'AI',
      tokenImage: 'assets/images/bot.png',
      currentPrice: 0.0,
      priceChange24h: 0.0,
      isActive: false,
      strategy: botType == 'ai_agent' ? 'AI Trading' : 'Single Coin',
      solPerTrade: 0.1,
      totalTrades: 0,
      winRate: 0.0,
      totalEarned: 0.0,
      trades: [],
    );

    setState(() {
      _bots.add(newBot);
    });

    Navigator.pop(context);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${newBot.name} created successfully!'),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
