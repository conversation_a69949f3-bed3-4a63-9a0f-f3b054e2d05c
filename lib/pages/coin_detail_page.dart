import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../models/coin_model.dart';
import '../services/app_lifecycle_service.dart';
import '../services/supabase_coin_service.dart';
import '../widgets/gradient_button.dart';
import '../widgets/coin_chart.dart';
import '../widgets/ai_prediction.dart';
import '../widgets/bot_config_modal.dart';
import '../utils/currency_formatter.dart';

class CoinDetailPage extends StatefulWidget {
  final String symbol;
  final CoinData coin;

  const CoinDetailPage({super.key, required this.symbol, required this.coin});

  @override
  State<CoinDetailPage> createState() => _CoinDetailPageState();
}

class _CoinDetailPageState extends State<CoinDetailPage> {
  final AppLifecycleService _lifecycleService = AppLifecycleService();
  final SupabaseCoinService _supabaseCoinService = SupabaseCoinService();

  late CoinData _currentCoin;
  StreamSubscription<List<CoinData>>? _coinStreamSubscription;
  StreamSubscription<CoinData>? _singleCoinStreamSubscription;
  Timer? _priceUpdateTimer;
 String _coinSymbol = "";

  @override
  void initState() {
    super.initState();
    _currentCoin = widget.coin;

    // Notify lifecycle service that we're on coin detail page
    _lifecycleService.navigateToCoinDetail(widget.coin.symbol);

    // Start real-time updates for this specific coin
    _startRealTimeUpdates();
   


  }

  void getTokenData() async {
    final tradingViewSymbol = await fetchDexPairSymbol("solana",_currentCoin.tokenAddress ?? "");
    setState(() {
      _coinSymbol = tradingViewSymbol ?? "";
    });
  }


  @override
  void dispose() {
    // Stop real-time updates
    _coinStreamSubscription?.cancel();
    _singleCoinStreamSubscription?.cancel();
    _priceUpdateTimer?.cancel();

    // Cancel single coin subscription
    _supabaseCoinService.cancelSingleCoinSubscription();

    // Navigate back to coins page when leaving detail page
    _lifecycleService.navigateBackFromCoinDetail();
    super.dispose();
  }

  void _startRealTimeUpdates() {
    print('🔄 Starting real-time updates for coin: ${_currentCoin.symbol} (${_currentCoin.tokenAddress ?? 'No token address'})');

    if (_currentCoin.tokenAddress != null) {
      // Subscribe to single coin updates using token address
      print('🎯 Setting up single coin subscription for: ${_currentCoin.tokenAddress}');

      _supabaseCoinService.initializeSingleCoinSubscription(_currentCoin.tokenAddress!);

      _singleCoinStreamSubscription = _supabaseCoinService.singleCoinStream.listen((updatedCoin) {
        if (mounted) {
          final oldMarketCap = _currentCoin.currentPrice ?? 0; // currentPrice is market_cap
          final newMarketCap = updatedCoin.currentPrice ?? 0;
          final changePercent = updatedCoin.market_cap_change_percent ?? 0;

          print('💰 Single coin update for ${updatedCoin.symbol}:');
          print('   Market Cap: ${CurrencyFormatter.format(oldMarketCap)} → ${CurrencyFormatter.format(newMarketCap)}');
          print('   Change: ${CurrencyFormatter.formatPercentage(changePercent)}');

          setState(() {
            _currentCoin = updatedCoin;
          });
        }
      });
    } else {
      // Use the current filtered stream from coin page instead of general stream
      print('⚠️ No token address available, using current filtered stream');

      _coinStreamSubscription = _supabaseCoinService.coinStream.listen((coins) {
        if (mounted) {
          print('📡 Received ${coins.length} coins from filtered stream');

          // Find the current coin by symbol or name
          final updatedCoin = coins.firstWhere(
            (coin) => coin.symbol.toLowerCase() == _currentCoin.symbol.toLowerCase() ||
                      coin.name.toLowerCase() == _currentCoin.name.toLowerCase(),
            orElse: () => _currentCoin,
          );

          if (updatedCoin != _currentCoin) {
            final oldMarketCap = _currentCoin.currentPrice ?? 0; // currentPrice is market_cap
            final newMarketCap = updatedCoin.currentPrice ?? 0;
            final changePercent = updatedCoin.market_cap_change_percent ?? 0;

            print('💰 Market cap update for ${updatedCoin.symbol}:');
            print('   Market Cap: ${CurrencyFormatter.format(oldMarketCap)} → ${CurrencyFormatter.format(newMarketCap)}');
            print('   Change: ${CurrencyFormatter.formatPercentage(changePercent)}');

            setState(() {
              _currentCoin = updatedCoin;
            });
          }
        }
      });
    }

    // Start a fallback timer for manual refresh
    _priceUpdateTimer = Timer.periodic(const Duration(seconds: 60), (timer) {
      if (mounted && _lifecycleService.shouldEnableCoinDetailUpdates) {
        print('⏰ Fallback timer triggered - manual refresh');
        _refreshCoinData();
      }
    });
  }

  Future<void> _refreshCoinData() async {
    try {
      print('🔄 Manually refreshing coin data for ${_currentCoin.symbol}');

      // Try to get fresh data from Supabase
      List<CoinData> freshCoins;

      if (_currentCoin.tokenAddress != null) {
        // Search by token address first
        freshCoins = await _supabaseCoinService.searchCoins(_currentCoin.tokenAddress!);
      } else {
        // Fallback to symbol search
        freshCoins = await _supabaseCoinService.searchCoins(_currentCoin.symbol);
      }

      if (freshCoins.isNotEmpty) {
        final freshCoin = freshCoins.first;
        final oldMarketCap = _currentCoin.currentPrice ?? 0; // currentPrice is market_cap
        final newMarketCap = freshCoin.currentPrice ?? 0;
        final changePercent = freshCoin.market_cap_change_percent ?? 0;

        print('💰 Manual refresh - Market cap for ${freshCoin.symbol}:');
        print('   Market Cap: ${CurrencyFormatter.format(oldMarketCap)} → ${CurrencyFormatter.format(newMarketCap)}');
        print('   Change: ${CurrencyFormatter.formatPercentage(changePercent)}');

        if (mounted) {
          setState(() {
            _currentCoin = freshCoin;
          });
        }
      } else {
        print('❌ No fresh data found for ${_currentCoin.symbol}');
      }
    } catch (e) {
      print('❌ Error refreshing coin data: $e');
    }
  }


  // Example: fetch DEX pair and build symbol
Future<String?> fetchDexPairSymbol(String chain, String tokenAddress) async {
  final url = Uri.parse(
    'https://api.dexscreener.com/token-pairs/v1/$chain/$tokenAddress',
  );
  final res = await http.get(url);
  if (res.statusCode == 200) {
    final data = jsonDecode(res.body) as List;
    if (data.isNotEmpty) {
      final pair = data[0];
      final dex = pair['dexId'];
      final base = pair['baseToken']['symbol'];
      final quote = pair['quoteToken']['symbol'];
      return '$base/$quote';
    }
  }
  return null;
}



  @override
  Widget build(BuildContext context) {
    final coin = _currentCoin;
    final isPositive =
        coin.market_cap_change_percent != null &&
        coin.market_cap_change_percent! >= 0;
    final changeColor = isPositive ? Colors.green : Colors.red;
    final subtitleColor = ThemeMode.system == ThemeMode.dark
        ? Colors.grey[400]
        : Colors.grey[600];
    return Scaffold(
      backgroundColor: const Color(0xFF0F0F0F),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(LucideIcons.arrowLeft, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Row(
          children: [
            // Coin Icon and Rank
           ( coin.image != null 
            && coin.image!.isNotEmpty)
                ? Image.network(
                    coin.image!,
                    width: 36,
                    height: 36,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Icon(
                      Icons.currency_bitcoin,
                      // color: textColor.withValues(alpha: 0.7),
                    ),
                  )
                : Center(
                    child: Text(
                      coin.symbol.substring(0, 1).toUpperCase(),
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
            SizedBox(width: 12),

            // Coin Name and Symbol
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (coin.displayName.isNotEmpty) ...[
                    Text(
                      coin.displayName,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 15,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ] else ...[
                    Text(
                      coin.displaySymbol.split(' / ')[0],
                      style: TextStyle(
                        fontWeight: FontWeight.w800,
                        fontSize: 15,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                  SizedBox(height: 6),
                  Row(
                    spacing: 5,
                    children: [
                      Text(
                        coin.displaySymbol,
                        style: TextStyle(
                          color: subtitleColor,
                          fontSize: 12,

                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                      ),
                      // Coin image
                      if (coin.image != null && coin.image!.isNotEmpty) ...[
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: Image.network(coin.image!, fit: BoxFit.cover),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),

            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                if ((coin.formattedPrice.isNotEmpty))
                  Text(
                    formatNumberCompact(coin.marketCap),
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 15),
                  ),
                const SizedBox(height: 4),
                if (coin.market_cap_change_percent != null)
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        isPositive
                            ? Icons.arrow_drop_up
                            : Icons.arrow_drop_down,
                        color: changeColor,
                        size: 16,
                      ),
                      Text(
                        coin.market_cap_change_percent!.toStringAsFixed(3),
                        style: TextStyle(
                          color: changeColor,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
              ],
            ),
          ],
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Real-time Chart
            CoinChart(
             
              symbol: _coinSymbol,
              currentPrice: coin.currentPrice ?? 0.0, tokenAddress: coin.tokenAddress ?? "",),

            // AI Prediction
            AIPrediction(
              symbol: coin.symbol,
              currentPrice: coin.currentPrice ?? 0.0,
            ),

            const SizedBox(height: 24),

            // Stats section
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  _buildStatRow('Market Cap', coin.formattedMarketCap),
                  const SizedBox(height: 16),
                  _buildStatRow('24h Volume', coin.formattedVolume),
                  const SizedBox(height: 16),
                  _buildStatRow(
                    'Rank',
                    coin.marketCapRank != null
                        ? '#${coin.marketCapRank}'
                        : 'N/A',
                  ),
                  const SizedBox(height: 16),
                  _buildStatRow(
                    'Supply',
                    coin.circulatingSupply != null
                        ? '${(coin.circulatingSupply! / 1000000).toStringAsFixed(1)}M'
                        : 'N/A',
                  ),
                ],
              ),
            ),
            // Bottom padding for safe area
            const SizedBox(height: 32),
          ],
        ),
      ),
      bottomNavigationBar: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: GradientButton(
            text: 'Start Trading',
            onPressed: _showCreateBotModal,
            icon: LucideIcons.bot,
          ),
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 16,
            color: Colors.white.withValues(alpha: 0.6),
            fontWeight: FontWeight.w500,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  void _showCreateBotModal() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CreateBotModal(
        coinSymbol: widget.symbol,
        onBotCreated: () {
          // Bot created callback
        },
      ),
    );
  }

  String formatNumberCompact(double? number) {
    if (number == null) return '';

    if (number >= 1e9) {
      return '${(number / 1e9).toStringAsFixed(1)}B';
    } else if (number >= 1e6) {
      return '${(number / 1e6).toStringAsFixed(1)}M';
    } else if (number >= 1e3) {
      return '${(number / 1e3).toStringAsFixed(1)}K';
    } else {
      return number.toStringAsFixed(2);
    }
  }
}
