import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../../services/cms_service.dart';
import '../../theme/app_theme.dart';
import '../../utils/responsive_helper.dart';

class DynamicCMSPage extends StatefulWidget {
  final String slug;
  final String fallbackTitle;
  final String fallbackContent;

  const DynamicCMSPage({
    super.key,
    required this.slug,
    required this.fallbackTitle,
    required this.fallbackContent,
  });

  @override
  State<DynamicCMSPage> createState() => _DynamicCMSPageState();
}

class _DynamicCMSPageState extends State<DynamicCMSPage> {
  final CMSService _cmsService = CMSService();
  CMSPage? _page;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadPage();
  }

  Future<void> _loadPage() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final page = await _cmsService.getPageBySlug(widget.slug);
      
      if (mounted) {
        setState(() {
          _page = page;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: ResponsiveHelper.isMobile(context) ? AppBar(
        title: Text(_getTitle()),
        centerTitle: false,
        elevation: 0,
        backgroundColor: Colors.transparent,
      ) : null,
      body: _buildBody(),
    );
  }

  String _getTitle() {
    return _page?.title ?? widget.fallbackTitle;
  }

  String _getContent() {
    return _page?.content ?? widget.fallbackContent;
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading content...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return _buildErrorState();
    }

    return _buildContent();
  }

  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: ResponsiveHelper.getPagePadding(context),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              LucideIcons.wifiOff,
              size: 64,
              color: Colors.orange,
            ),
            const SizedBox(height: 16),
            const Text(
              'Unable to load content',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            Text(
              'Showing offline content instead',
              style: TextStyle(color: Colors.grey[600]),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadPage,
              icon: const Icon(LucideIcons.refreshCw),
              label: const Text('Retry'),
            ),
            const SizedBox(height: 32),
            _buildFallbackContent(),
          ],
        ),
      ),
    );
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      padding: ResponsiveHelper.getPagePadding(context),
      child: Center(
        child: Container(
          constraints: BoxConstraints(
            maxWidth: ResponsiveHelper.getMaxContentWidth(context),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (ResponsiveHelper.isDesktop(context)) ...[
                Text(
                  _getTitle(),
                  style: TextStyle(
                    fontSize: ResponsiveHelper.getFontSize(context, 32),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 32),
              ],

              _buildContentCard(),

              if (_page != null) ...[
                const SizedBox(height: 24),
                _buildLastUpdatedInfo(),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContentCard() {
    return Card(
      elevation: ResponsiveHelper.isDesktop(context) ? 2 : 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(ResponsiveHelper.isDesktop(context) ? 32 : 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (ResponsiveHelper.isMobile(context)) ...[
              Text(
                _getTitle(),
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),
            ],
            
            _buildFormattedContent(_getContent()),
          ],
        ),
      ),
    );
  }

  Widget _buildFormattedContent(String content) {
    // Simple content formatting - split by double newlines for paragraphs
    final paragraphs = content.split('\n\n');
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: paragraphs.map((paragraph) {
        if (paragraph.trim().isEmpty) return const SizedBox.shrink();
        
        // Check if it's a question (starts with Q:)
        if (paragraph.trim().startsWith('Q:')) {
          return _buildQuestionAnswer(paragraph);
        }
        
        // Regular paragraph
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: Text(
            paragraph.trim(),
            style: TextStyle(
              fontSize: ResponsiveHelper.getFontSize(context, 16),
              height: 1.6,
              color: Theme.of(context).textTheme.bodyLarge?.color,
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildQuestionAnswer(String qaText) {
    final lines = qaText.split('\n');
    String question = '';
    String answer = '';
    
    for (final line in lines) {
      if (line.trim().startsWith('Q:')) {
        question = line.trim().substring(2).trim();
      } else if (line.trim().startsWith('A:')) {
        answer = line.trim().substring(2).trim();
      }
    }
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            question,
            style: TextStyle(
              fontSize: ResponsiveHelper.getFontSize(context, 18),
              fontWeight: FontWeight.w600,
              color: AppTheme.primaryColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            answer,
            style: TextStyle(
              fontSize: ResponsiveHelper.getFontSize(context, 16),
              height: 1.6,
              color: Theme.of(context).textTheme.bodyLarge?.color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFallbackContent() {
    return Card(
      elevation: 1,
      color: Colors.orange.withValues(alpha: 0.1),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(LucideIcons.archive, color: Colors.orange),
                const SizedBox(width: 8),
                Text(
                  widget.fallbackTitle,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.orange,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildFormattedContent(widget.fallbackContent),
          ],
        ),
      ),
    );
  }

  Widget _buildLastUpdatedInfo() {
    if (_page == null) return const SizedBox.shrink();
    
    return Card(
      elevation: 1,
      color: Colors.grey.withValues(alpha: 0.1),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            const Icon(LucideIcons.clock, size: 16, color: Colors.grey),
            const SizedBox(width: 8),
            Text(
              'Last updated: ${_formatDate(_page!.updatedAt)}',
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
    } else {
      return 'Recently';
    }
  }
}
