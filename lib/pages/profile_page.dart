import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../services/supabase_auth_service.dart';
import '../theme/app_theme.dart';
import '../utils/responsive_helper.dart';
import 'profile/edit_profile_page.dart';
import 'profile/notification_settings_page.dart';
import 'profile/dynamic_cms_page.dart';
import 'profile/contact_us_page.dart';
import '../debug/database_test_widget.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  final SupabaseAuthService _authService = SupabaseAuthService();
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUserProfile();
  }

  Future<void> _loadUserProfile() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Just check if user profile can be loaded, but we'll use auth data directly
      await _authService.getUserProfile();
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading user profile: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading profile: $e'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: ResponsiveHelper.isMobile(context)
          ? AppBar(
              title: const Text(
                'Profile',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              centerTitle: false,
              elevation: 0,
              backgroundColor: Colors.transparent,
            )
          : null,
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : ResponsiveHelper.isDesktop(context)
          ? _buildDesktopLayout()
          : _buildMobileLayout(),
    );
  }

  Widget _buildDesktopLayout() {
    return SingleChildScrollView(
      padding: ResponsiveHelper.getPagePadding(context),
      child: Center(
        child: Container(
          constraints: BoxConstraints(
            maxWidth: ResponsiveHelper.getMaxContentWidth(context),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Left column - Profile header
              Expanded(
                flex: 1,
                child: Column(
                  children: [
                    _buildProfileHeader(),
                    const SizedBox(height: 32),
                    _buildLogoutButton(),
                  ],
                ),
              ),
              const SizedBox(width: 32),
              // Right column - Menu sections
              Expanded(flex: 2, child: _buildMenuSection()),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMobileLayout() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          _buildProfileHeader(),
          const SizedBox(height: 32),
          _buildMenuSection(),
          const SizedBox(height: 32),
          _buildLogoutButton(),
        ],
      ),
    );
  }

  Widget _buildProfileHeader() {
    final user = _authService.currentUser;
    final userName =
        user?.userMetadata?['full_name'] ??
        user?.userMetadata?['name'] ??
        user?.email?.split('@')[0] ??
        'User';
    final userEmail = user?.email ?? '';
    final userPicture = user?.userMetadata?['avatar_url'] as String?;

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        children: [
          Stack(
            children: [
              CircleAvatar(
                radius: 50,
                backgroundColor: Colors.white.withOpacity(0.2),
                backgroundImage: userPicture != null && userPicture.isNotEmpty
                    ? NetworkImage(userPicture)
                    : null,
                child: userPicture == null || userPicture.isEmpty
                    ? const Icon(
                        LucideIcons.user,
                        size: 40,
                        color: Colors.white,
                      )
                    : null,
              ),
              Positioned(
                bottom: 0,
                right: 0,
                child: GestureDetector(
                  onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const EditProfilePage(),
                    ),
                  ),
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      LucideIcons.camera,
                      size: 16,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            userName,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
            maxLines: 2,
          ),
          const SizedBox(height: 4),
          Text(
            userEmail,
            style: TextStyle(
              fontSize: 16,
              color: Colors.white.withOpacity(0.8),
            ),
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
            maxLines: 2,
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 8,
                  height: 8,
                  decoration: const BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 8),
                const Text(
                  'Active',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuSection() {
    if (ResponsiveHelper.isDesktop(context)) {
      return _buildDesktopMenuGrid();
    }
    return _buildMobileMenuList();
  }

  Widget _buildDesktopMenuGrid() {
    final menuItems = _getAllMenuItems();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Account Settings',
          style: TextStyle(
            fontSize: ResponsiveHelper.getFontSize(context, 24),
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 24),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: ResponsiveHelper.getGridColumns(context),
            crossAxisSpacing: ResponsiveHelper.getCardSpacing(context),
            mainAxisSpacing: ResponsiveHelper.getCardSpacing(context),
            childAspectRatio: 1.2,
          ),
          itemCount: menuItems.length,
          itemBuilder: (context, index) =>
              _buildDesktopMenuCard(menuItems[index]),
        ),
      ],
    );
  }

  Widget _buildMobileMenuList() {
    return Column(
      children: [
        _buildMenuGroup('Account', [
          _buildMenuItem(
            icon: LucideIcons.user,
            title: 'Edit Profile',
            subtitle: 'Update your personal information',
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const EditProfilePage()),
            ),
          ),
          _buildMenuItem(
            icon: LucideIcons.bell,
            title: 'Notifications',
            subtitle: 'Manage your notification preferences',
            onTap: () {},
          ),
          _buildMenuItem(
            icon: LucideIcons.shield,
            title: 'Security',
            subtitle: 'Password and security settings',
            onTap: () {},
          ),
        ]),
        const SizedBox(height: 24),
        _buildMenuGroup('Support', [
          _buildMenuItem(
            icon: LucideIcons.handHelping,
            title: 'FAQ',
            subtitle: 'Frequently asked questions',
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const DynamicCMSPage(
                  slug: 'faq',
                  fallbackTitle: 'Frequently Asked Questions',
                  fallbackContent:
                      'Q: How does DexTrip work?\nA: DexTrip helps you track meme coins in real-time.\n\nQ: Is DexTrip free?\nA: Yes, basic features are free.',
                ),
              ),
            ),
          ),
          _buildMenuItem(
            icon: LucideIcons.messageCircle,
            title: 'Contact Us',
            subtitle: 'Get help from our support team',
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const ContactUsPage()),
            ),
          ),
          _buildMenuItem(
            icon: LucideIcons.info,
            title: 'About Us',
            subtitle: 'Learn more about DexTrip',
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const DynamicCMSPage(
                  slug: 'about-us',
                  fallbackTitle: 'About Us',
                  fallbackContent:
                      'DexTrip is a platform to track and analyze meme coins in real-time. We provide comprehensive tools for cryptocurrency enthusiasts.',
                ),
              ),
            ),
          ),
        ]),
        const SizedBox(height: 24),
        _buildMenuGroup('Debug', [
          _buildMenuItem(
            icon: LucideIcons.bug,
            title: 'Database Test',
            subtitle: 'Test trading bot database operations',
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const DatabaseTestWidget(),
              ),
            ),
          ),
        ]),
        const SizedBox(height: 24),
        _buildMenuGroup('Legal', [
          _buildMenuItem(
            icon: LucideIcons.fileText,
            title: 'Privacy Policy',
            subtitle: 'How we handle your data',
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const DynamicCMSPage(
                  slug: 'privacy-policy',
                  fallbackTitle: 'Privacy Policy',
                  fallbackContent:
                      'We respect your privacy and do not share your personal data. Your information is secure with us.',
                ),
              ),
            ),
          ),
          _buildMenuItem(
            icon: LucideIcons.scroll,
            title: 'Terms & Conditions',
            subtitle: 'Terms of service',
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const DynamicCMSPage(
                  slug: 'terms-and-conditions',
                  fallbackTitle: 'Terms and Conditions',
                  fallbackContent:
                      'By using this app, you agree to our terms of service. Please read these terms carefully.',
                ),
              ),
            ),
          ),
        ]),
      ],
    );
  }

  Widget _buildMenuGroup(String title, List<Widget> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 4, bottom: 12),
          child: Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).textTheme.headlineSmall?.color,
            ),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(children: items),
        ),
      ],
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, color: AppTheme.primaryColor, size: 20),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 14,
                        color: Theme.of(
                          context,
                        ).textTheme.bodySmall?.color?.withOpacity(0.7),
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                    ),
                  ],
                ),
              ),
              Icon(
                LucideIcons.chevronRight,
                size: 16,
                color: Theme.of(
                  context,
                ).textTheme.bodySmall?.color?.withOpacity(0.5),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLogoutButton() {
    return SizedBox(
      width: double.infinity,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.red[400]!, Colors.red[600]!],
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: _handleLogout,
            borderRadius: BorderRadius.circular(16),
            child: const Padding(
              padding: EdgeInsets.symmetric(vertical: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(LucideIcons.logOut, color: Colors.white, size: 20),
                  SizedBox(width: 8),
                  Text(
                    'Sign Out',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _handleLogout() async {
    try {
      // Show confirmation dialog
      final shouldLogout = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Sign Out'),
          content: const Text('Are you sure you want to sign out?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              child: const Text('Sign Out'),
            ),
          ],
        ),
      );

      if (shouldLogout == true) {
        // Just sign out - the main app's auth listener will handle everything:
        // 1. Set _isSignedIn = false
        // 2. Navigate to Coins page (index 0)
        // 3. Reinitialize pages to show "Get Started" button
        // 4. Show success message
        await _authService.signOut();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Error signing out: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  List<Map<String, dynamic>> _getAllMenuItems() {
    return [
      {
        'icon': LucideIcons.user,
        'title': 'Edit Profile',
        'subtitle': 'Update your personal information',
        'onTap': () => Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const EditProfilePage()),
        ),
      },
      {
        'icon': LucideIcons.bell,
        'title': 'Notifications',
        'subtitle': 'Manage your notification preferences',
        'onTap': () => Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const NotificationSettingsPage(),
          ),
        ),
      },
      {
        'icon': LucideIcons.shield,
        'title': 'Security',
        'subtitle': 'Password and security settings',
        'onTap': () => ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Security settings coming soon!')),
        ),
      },
      {
        'icon': LucideIcons.handHelping,
        'title': 'FAQ',
        'subtitle': 'Frequently asked questions',
        'onTap': () => Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const DynamicCMSPage(
              slug: 'faq',
              fallbackTitle: 'Frequently Asked Questions',
              fallbackContent:
                  'Q: How does DexTrip work?\nA: DexTrip helps you track meme coins in real-time.\n\nQ: Is DexTrip free?\nA: Yes, basic features are free.',
            ),
          ),
        ),
      },
      {
        'icon': LucideIcons.messageCircle,
        'title': 'Contact Us',
        'subtitle': 'Get help and support',
        'onTap': () => Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const ContactUsPage()),
        ),
      },
      {
        'icon': LucideIcons.info,
        'title': 'About Us',
        'subtitle': 'Learn more about DexTrip',
        'onTap': () => Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const DynamicCMSPage(
              slug: 'about-us',
              fallbackTitle: 'About Us',
              fallbackContent:
                  'DexTrip is a platform to track and analyze meme coins in real-time. We provide comprehensive tools for cryptocurrency enthusiasts.',
            ),
          ),
        ),
      },
      {
        'icon': LucideIcons.fileText,
        'title': 'Privacy Policy',
        'subtitle': 'Our privacy and data policy',
        'onTap': () => Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const DynamicCMSPage(
              slug: 'privacy-policy',
              fallbackTitle: 'Privacy Policy',
              fallbackContent:
                  'We respect your privacy and do not share your personal data. Your information is secure with us.',
            ),
          ),
        ),
      },
      {
        'icon': LucideIcons.scroll,
        'title': 'Terms & Conditions',
        'subtitle': 'Terms of service',
        'onTap': () => Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const DynamicCMSPage(
              slug: 'terms-and-conditions',
              fallbackTitle: 'Terms and Conditions',
              fallbackContent:
                  'By using this app, you agree to our terms of service. Please read these terms carefully.',
            ),
          ),
        ),
      },
    ];
  }

  Widget _buildDesktopMenuCard(Map<String, dynamic> item) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: item['onTap'],
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(item['icon'], size: 28, color: AppTheme.primaryColor),
              const SizedBox(height: 8),
              Flexible(
                child: Text(
                  item['title'],
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(height: 4),
              Flexible(
                child: Text(
                  item['subtitle'],
                  style: TextStyle(
                    fontSize: 11,
                    color: Theme.of(
                      context,
                    ).textTheme.bodySmall?.color?.withOpacity(0.7),
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
