import 'package:dextrip_app/widgets/bot_config_modal.dart';
import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'dart:async';
import '../widgets/swipeable_card_stack.dart';
import '../widgets/agent_selector_modal.dart';
import '../widgets/trading_recommendation_card.dart';
import '../models/coin_model.dart';
import '../models/trading_bot_model.dart';
import '../theme/app_theme.dart';

class Recommendation {
  final String id;
  final String agentName;
  final String agentAvatarUrl;
  final CoinData coin;
  final String direction; // Long or Short
  final String description;
  final String timeAgo;
  final double targetPrice;
  final double stopLoss;
  final List<double> chartData;
  final double takeProfit;
  final double stopLossPercent;
  final String confidence;

  Recommendation({
    required this.id,
    required this.agentName,
    required this.agentAvatarUrl,
    required this.coin,
    required this.direction,
    required this.description,
    required this.timeAgo,
    required this.targetPrice,
    required this.stopLoss,
    required this.chartData,
    required this.takeProfit,
    required this.stopLossPercent,
    required this.confidence,
  });
}

class RecommendationPage extends StatefulWidget {
  const RecommendationPage({super.key});

  @override
  State<RecommendationPage> createState() => _RecommendationPageState();
}

class _RecommendationPageState extends State<RecommendationPage> {
  late List<Recommendation> _recommendations;
  final GlobalKey<SwipeableCardStackState> _cardStackKey = GlobalKey();
  String _selectedAgentId = 'donalt';
  String _selectedAgentName = 'DonAlt';

  // Countdown timer state
  Timer? _countdownTimer;
  int _countdownSeconds = 10;
  bool _isTradeButtonActive = false;
  bool _showTradeOutline = false;
  bool _isSkipButtonActive = false;
  bool _showSkipOutline = false;

  // Card swipe progress state
  double _swipeProgress = 0.0;
  bool _isCardSwipingRight = false;
  bool _isCardBeingDragged = false;

  @override
  void initState() {
    super.initState();
    _recommendations = _generateDummyData();
    _startCountdown();
  }

  @override
  void dispose() {
    _countdownTimer?.cancel();
    super.dispose();
  }

  void _startCountdown() {
    _countdownSeconds = 10;
    _isTradeButtonActive = false;
    _showTradeOutline = false;
    _isSkipButtonActive = false;
    _showSkipOutline = false;
    _swipeProgress = 0.0;
    _isCardSwipingRight = false;

    _countdownTimer?.cancel();
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        // Only countdown if card is not being dragged
        if (!_isCardBeingDragged && _countdownSeconds > 0) {
          _countdownSeconds--;
        } else if (!_isCardBeingDragged && _countdownSeconds <= 0) {
          // Auto-advance to next card
          _cardStackKey.currentState?.swipeLeft();
          _startCountdown(); // Restart countdown for next card
        }
      });
    });
  }

  void _onSwipeProgress(double progress, bool isRight) {
    setState(() {
      _swipeProgress = progress;
      _isCardSwipingRight = isRight;
    });
  }

  void _onDragStateChanged(bool isDragging) {
    setState(() {
      _isCardBeingDragged = isDragging;
    });
  }

  void _onTradeButtonPressed() {
    setState(() {
      _isTradeButtonActive = true;
      _showTradeOutline = true;
      // Trigger card swipe animation immediately
      _swipeProgress = 0.8;
      _isCardSwipingRight = true;
    });

    // Show outline for 1 second then execute trade
    Timer(const Duration(seconds: 1), () {
      _cardStackKey.currentState?.swipeRight();
    });
  }

  void _onSkipButtonPressed() {
    setState(() {
      _isSkipButtonActive = true;
      _showSkipOutline = true;
      // Trigger card swipe animation immediately
      _swipeProgress = 0.8;
      _isCardSwipingRight = false;
    });

    // Show outline for 1 second then execute skip
    Timer(const Duration(seconds: 1), () {
      _cardStackKey.currentState?.swipeLeft();
    });
  }

  void _onSwipeRight(int index) {
    final rec = _recommendations[index];
    _startTrade(rec);
    _startCountdown(); // Restart countdown for next card
  }

  void _onSwipeLeft(int index) {
    final rec = _recommendations[index];
    _skipRecommendation(rec);
    _startCountdown(); // Restart countdown for next card
  }

  void _startTrade(Recommendation rec) {
    // TODO: Navigate to trade setup page
  }

  void _skipRecommendation(Recommendation rec) {
    // Just skip silently
  }

  void _changeAgent() {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final colorScheme = Theme.of(context).colorScheme;
    
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => AgentSelectorModal(
        currentAgentId: _selectedAgentId,
        onAgentSelected: (agent) {
          setState(() {
            _selectedAgentId = agent.id;
            _selectedAgentName = agent.name;
          });
          // TODO: Refresh recommendations for new agent
        },
      ),
    );
  }

  void _openIndicators() {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final colorScheme = Theme.of(context).colorScheme;
    
    // Create a dummy bot for configuration
    final dummyBot = TradingBot(
      id: 'recommendation_bot',
      name: 'Recommendation Bot',
      tokenSymbol: 'BTC',
      tokenImage: '',
      currentPrice: 0.0,
      priceChange24h: 0.0,
      isActive: false,
      strategy: 'DCA',
      solPerTrade: 0.1,
      totalTrades: 0,
      winRate: 0.0,
      totalEarned: 0.0,
      trades: [],
    );

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => BotConfigModal(bot: dummyBot),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    
    return Scaffold(
      backgroundColor: colorScheme.background,
      appBar: AppBar(
        automaticallyImplyLeading: false,
        elevation: 0,
        backgroundColor: colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        shadowColor: colorScheme.shadow.withValues(alpha: 0.1),
        title: Row(
          children: [
            // Agent selector
            GestureDetector(
              onTap: _changeAgent,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: colorScheme.surfaceVariant.withValues(alpha: 0.5),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: colorScheme.outline.withValues(alpha: 0.3),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: colorScheme.shadow.withValues(alpha: 0.05),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    CircleAvatar(
                      backgroundImage: const AssetImage('assets/avatars/agent.png'),
                      radius: 18,
                      backgroundColor: colorScheme.primaryContainer,
                    ),
                    const SizedBox(width: 12),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              _selectedAgentName,
                              style: textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: colorScheme.onSurface,
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(width: 6),
                            Icon(
                              LucideIcons.chevronDown,
                              size: 14,
                              color: colorScheme.onSurfaceVariant,
                            ),
                          ],
                        ),
                        Text(
                          'AI Trading Agent',
                          style: textTheme.bodySmall?.copyWith(
                            color: colorScheme.onSurfaceVariant,
                            fontSize: 11,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const Spacer(),
            // Indicator settings
            Container(
              decoration: BoxDecoration(
                color: colorScheme.primaryContainer.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: colorScheme.primary.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: IconButton(
                icon: Icon(
                  LucideIcons.settings, 
                  color: colorScheme.primary, 
                  size: 20,
                ),
                onPressed: _openIndicators,
                tooltip: 'Trading Settings',
              ),
            ),
          ],
        ),
      ),
      body: SafeArea(
        child: _recommendations.isEmpty
            ? _buildEmptyState()
            : _buildRecommendationContent(),
      ),
    );
  }

  Widget _buildEmptyState() {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    
    return Center(
      child: Container(
        margin: const EdgeInsets.all(24),
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: colorScheme.surface,
          borderRadius: BorderRadius.circular(24),
          border: Border.all(
            color: colorScheme.outline.withValues(alpha: 0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: colorScheme.shadow.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: colorScheme.primaryContainer.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Icon(
                LucideIcons.trendingUp,
                size: 48,
                color: colorScheme.primary,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'No Recommendations Available',
              style: textTheme.headlineSmall?.copyWith(
                color: colorScheme.onSurface,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              'Check back later for new trading opportunities from your selected AI agent',
              style: textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  _recommendations = _generateDummyData();
                });
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: colorScheme.primary,
                foregroundColor: colorScheme.onPrimary,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              icon: Icon(
                LucideIcons.refreshCw,
                size: 16,
                color: colorScheme.onPrimary,
              ),
              label: Text(
                'Refresh',
                style: textTheme.labelLarge?.copyWith(
                  color: colorScheme.onPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecommendationContent() {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    
    return Column(
      children: [
        // Status indicator
        Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: colorScheme.outline.withValues(alpha: 0.2),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: colorScheme.shadow.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                width: 10,
                height: 10,
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(5),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.green.withValues(alpha: 0.3),
                      blurRadius: 4,
                      spreadRadius: 1,
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'Live Trading Signals',
                style: textTheme.titleSmall?.copyWith(
                  color: colorScheme.onSurface,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${_recommendations.length}',
                  style: textTheme.labelSmall?.copyWith(
                    color: colorScheme.onPrimaryContainer,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
        
        // Card stack
        Expanded(
          child: SwipeableCardStack(
            key: _cardStackKey,
            cards: _recommendations
                .map(
                  (rec) => SwipeableRecommendationCard(
                    recommendation: rec,
                    swipeProgress: _swipeProgress,
                    isSwipingRight: _isCardSwipingRight,
                  ),
                )
                .toList(),
            onSwipe: (index, direction) {
              if (direction == SwipeDirection.right) {
                _onSwipeRight(index);
              } else {
                _onSwipeLeft(index);
              }
            },
            onSwipeProgress: _onSwipeProgress,
            onDragStateChange: _onDragStateChanged,
            cardHeight: MediaQuery.of(context).size.height * 0.6,
          ),
        ),

        // Fixed buttons at bottom
        _buildBottomControls(),
      ],
    );
  }

  Widget _buildBottomControls() {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: colorScheme.outline.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Timer indicator
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: colorScheme.surfaceVariant.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: colorScheme.outline.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  LucideIcons.clock,
                  size: 14,
                  color: colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 8),
                Text(
                  'Auto-advance in ${_countdownSeconds}s',
                  style: textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(child: _buildSkipButton()),
              const SizedBox(width: 16),
              Expanded(child: _buildTradeButton()),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSkipButton() {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      decoration: BoxDecoration(
        gradient: _isSkipButtonActive
            ? const LinearGradient(
                colors: [Color(0xFFFF4444), Color(0xFFCC0000)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              )
            : null,
        color: _isSkipButtonActive 
            ? null 
            : colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: _showSkipOutline
            ? Border.all(color: Colors.red, width: 2)
            : Border.all(
                color: colorScheme.outline.withValues(alpha: 0.3),
                width: 1,
              ),
        boxShadow: _isSkipButtonActive ? [
          BoxShadow(
            color: Colors.red.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ] : [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: _onSkipButtonPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              LucideIcons.x,
              color: _isSkipButtonActive
                  ? Colors.white
                  : colorScheme.onSurface,
              size: 16,
            ),
            const SizedBox(width: 8),
            Text(
              'Skip',
              style: textTheme.labelLarge?.copyWith(
                color: _isSkipButtonActive
                    ? Colors.white
                    : colorScheme.onSurface,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTradeButton() {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      decoration: BoxDecoration(
        gradient: _isTradeButtonActive 
            ? AppTheme.buttonGradient 
            : LinearGradient(
                colors: [colorScheme.primary, colorScheme.primary],
              ),
        borderRadius: BorderRadius.circular(12),
        border: _showTradeOutline
            ? Border.all(color: AppTheme.primaryColor, width: 2)
            : null,
        boxShadow: [
          BoxShadow(
            color: (_isTradeButtonActive ? AppTheme.primaryColor : colorScheme.primary)
                .withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: _onTradeButtonPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Trade',
              style: textTheme.labelLarge?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 8),
            const Icon(
              LucideIcons.arrowRight,
              color: Colors.white,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  List<Recommendation> _generateDummyData() {
    return [
      Recommendation(
        id: '1',
        agentName: 'DonAlt',
        agentAvatarUrl: 'assets/avatars/agent.png',
        coin: CoinData(
          id: 'ethereum',
          symbol: 'ETH',
          name: 'Ethereum',
          image:
              'https://assets.coingecko.com/coins/images/279/large/ethereum.png',
          currentPrice: 3693.22,
          marketCap: 444000000000,
          marketCapRank: 2,
          priceChangePercentage24h: 2.5,
          totalVolume: 15000000000,
        ),
        direction: 'Long',
        description:
            'ETHUSD showing strength! Moderate bullish momentum means it\'s time to go long with caution. Target Price: \$3780, Stop Loss: \$3540.',
        timeAgo: '16 minutes ago',
        targetPrice: 3780.0,
        stopLoss: 3540.0,
        chartData: [0.2, 0.4, 0.3, 0.7, 0.5, 0.8, 0.6, 0.9, 0.7, 0.8],
        takeProfit: 15.0,
        stopLossPercent: -17.0,
        confidence: 'High',
      ),
      Recommendation(
        id: '2',
        agentName: 'DonAlt',
        agentAvatarUrl: 'assets/avatars/agent.png',
        coin: CoinData(
          id: 'bitcoin',
          symbol: 'BTC',
          name: 'Bitcoin',
          image:
              'https://assets.coingecko.com/coins/images/1/large/bitcoin.png',
          currentPrice: 62150.0,
          marketCap: 1230000000000,
          marketCapRank: 1,
          priceChangePercentage24h: -1.8,
          totalVolume: 28000000000,
        ),
        direction: 'Short',
        description:
            'Bitcoin showing weakness at resistance. Technical indicators suggest a pullback is imminent. Consider shorting with tight risk management.',
        timeAgo: '32 minutes ago',
        targetPrice: 60000.0,
        stopLoss: 64000.0,
        chartData: [0.8, 0.7, 0.6, 0.4, 0.5, 0.3, 0.4, 0.2, 0.3, 0.1],
        takeProfit: -12.0,
        stopLossPercent: 8.0,
        confidence: 'Medium',
      ),
      Recommendation(
        id: '3',
        agentName: 'DonAlt',
        agentAvatarUrl: 'assets/avatars/agent.png',
        coin: CoinData(
          id: 'solana',
          symbol: 'SOL',
          name: 'Solana',
          image:
              'https://assets.coingecko.com/coins/images/4128/large/solana.png',
          currentPrice: 162.50,
          marketCap: 76000000000,
          marketCapRank: 5,
          priceChangePercentage24h: 4.2,
          totalVolume: 3200000000,
        ),
        direction: 'Long',
        description:
            'Solana breaking out of consolidation pattern. Strong volume confirms the move. Perfect setup for a swing trade.',
        timeAgo: '1 hour ago',
        targetPrice: 180.0,
        stopLoss: 145.0,
        chartData: [0.3, 0.2, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 0.85, 0.9],
        takeProfit: 22.0,
        stopLossPercent: -15.0,
        confidence: 'High',
      ),
    ];
  }
}
