import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../theme/app_theme.dart';
import '../services/wallet_service.dart';
import '../utils/toast_utils.dart';

class WalletCreationPage extends StatefulWidget {
  final String walletType;

  const WalletCreationPage({super.key, required this.walletType});

  @override
  State<WalletCreationPage> createState() => _WalletCreationPageState();
}

class _WalletCreationPageState extends State<WalletCreationPage> {
  final WalletService _walletService = WalletService();
  final _nameController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isCreating = false;

  @override
  void dispose() {
    _nameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).brightness == Brightness.dark
          ? Colors.grey[900]
          : Colors.grey[50],
      appBar: AppBar(
        title: Text(
          'Create ${widget.walletType.substring(0, 1).toUpperCase()}${widget.walletType.substring(1)} Wallet',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Info card
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.grey[850]?.withValues(alpha: 0.8)
                    : Colors.white.withValues(alpha: 0.8),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: _getWalletTypeColor(), width: 2),
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.black.withValues(alpha: 0.3)
                        : Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          color: _getWalletTypeColor().withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(25),
                          border: Border.all(
                            color: _getWalletTypeColor(),
                            width: 2,
                          ),
                        ),
                        child: Icon(
                          _getWalletTypeIcon(),
                          color: _getWalletTypeColor(),
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '${widget.walletType.substring(0, 1).toUpperCase()}${widget.walletType.substring(1)} Wallet',
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              widget.walletType == 'paper'
                                  ? 'Offline storage for maximum security'
                                  : 'Connect to blockchain network',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  if (widget.walletType == 'solana') ...[
                    const Text(
                      'Features:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    _buildFeatureItem(
                      '🔐',
                      'Generates new public/private key pair',
                    ),
                    _buildFeatureItem('🌐', 'Real Solana blockchain address'),
                    _buildFeatureItem(
                      '💰',
                      'Can receive actual SOL and tokens',
                    ),
                    _buildFeatureItem('🔒', 'Private key stored securely'),
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.orange[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.orange[200]!),
                      ),
                      child: const Text(
                        '⚠️ Keep your private key safe! Anyone with access can control your funds.',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.orange,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ] else ...[
                    const Text(
                      'About Paper Wallets:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    _buildFeatureItem('📄', 'Simple wallet for testing'),
                    _buildFeatureItem('💰', 'Custom SOL balance'),
                    _buildFeatureItem('🚫', 'No real blockchain address'),
                    _buildFeatureItem('🎯', 'Perfect for demos and learning'),
                  ],
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Form
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.grey[850]?.withValues(alpha: 0.8)
                    : Colors.white.withValues(alpha: 0.8),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.grey[700]!
                      : Colors.grey[300]!,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).brightness == Brightness.dark
                        ? Colors.black.withValues(alpha: 0.3)
                        : Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Wallet Details',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: _nameController,
                    decoration: InputDecoration(
                      labelText: 'Wallet Name',
                      hintText: 'My ${widget.walletType.substring(0, 1).toUpperCase()}${widget.walletType.substring(1)} Wallet',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(
                          color: _getWalletTypeColor(),
                          width: 2,
                        ),
                      ),
                      prefixIcon: const Icon(LucideIcons.wallet),
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: _passwordController,
                    obscureText: true,
                    decoration: InputDecoration(
                      labelText: 'Password',
                      hintText: 'Enter password to encrypt wallet',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(
                          color: _getWalletTypeColor(),
                          width: 2,
                        ),
                      ),
                      prefixIcon: const Icon(LucideIcons.lock),
                    ),
                  ),
                  const SizedBox(height: 24),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _isCreating ? null : () => _createWallet(),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _getWalletTypeColor(),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: _isCreating
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : Text(
                              'Create ${widget.walletType.substring(0, 1).toUpperCase()}${widget.walletType.substring(1)} Wallet',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(String emoji, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          Text(emoji, style: const TextStyle(fontSize: 16)),
          const SizedBox(width: 8),
          Expanded(child: Text(text, style: const TextStyle(fontSize: 14))),
        ],
      ),
    );
  }

  Color _getWalletTypeColor() {
    switch (widget.walletType) {
      case 'paper':
        return AppTheme.primaryColor;
      case 'solana':
        return const Color(0xFF6934C9); // Purple for Solana wallets
      case 'external':
        return const Color(0xFF2196F3); // Blue for external wallets
      default:
        return AppTheme.primaryColor;
    }
  }

  IconData _getWalletTypeIcon() {
    switch (widget.walletType) {
      case 'paper':
        return LucideIcons.fileText;
      case 'solana':
        return LucideIcons.key;
      case 'external':
        return LucideIcons.link;
      default:
        return LucideIcons.wallet;
    }
  }

  Future<void> _createWallet() async {
    if (_nameController.text.trim().isEmpty) {
      ToastUtils.showError(context, 'Please enter a wallet name');
      return;
    }

    // Check if Solana wallet creation
    if (widget.walletType == 'solana') {
      ToastUtils.showWarning(context, 'Solana wallet creation is under development');
      return;
    }

    if (widget.walletType == 'paper' && _passwordController.text.trim().isEmpty) {
      ToastUtils.showError(context, 'Please enter a password');
      return;
    }

    setState(() {
      _isCreating = true;
    });

    try {
      if (widget.walletType == 'paper') {
        await _walletService.createPaperWallet(
          name: _nameController.text.trim(),
          password: _passwordController.text.trim(),
        );
      } else {
        // For other wallet types, use the generic createWallet method
        await _walletService.createWallet(
          name: _nameController.text.trim(),
          type: widget.walletType,
        );
      }

      // Navigate back
      Navigator.pop(context);
      ToastUtils.showSuccess(context, '${widget.walletType} wallet created successfully!');
    } catch (e) {
      ToastUtils.showError(context, 'Error creating wallet: $e');
    } finally {
      setState(() {
        _isCreating = false;
      });
    }
  }

}
