import 'dart:async';
import 'package:flutter/foundation.dart';

enum AppPage {
  coins,
  bots,
  recommendations,
  trades,
  wallet,
  profile,
  coinDetail,
}

class AppLifecycleService {
  static final AppLifecycleService _instance = AppLifecycleService._();
  factory AppLifecycleService() => _instance;
  AppLifecycleService._();

  // Current page tracking
  AppPage _currentPage = AppPage.coins;
  String? _currentCoinSymbol; // For coin detail page

  // Stream controllers for page changes
  final StreamController<AppPage> _pageChangeController = 
      StreamController<AppPage>.broadcast();
  final StreamController<String?> _coinDetailController = 
      StreamController<String?>.broadcast();

  // App lifecycle state
  bool _isAppInForeground = true;

  // Getters
  AppPage get currentPage => _currentPage;
  String? get currentCoinSymbol => _currentCoinSymbol;
  bool get isAppInForeground => _isAppInForeground;

  // Streams
  Stream<AppPage> get pageChangeStream => _pageChangeController.stream;
  Stream<String?> get coinDetailStream => _coinDetailController.stream;

  // Check if real-time updates should be active
  bool get shouldEnableRealTimeUpdates {
    return _isAppInForeground && 
           (_currentPage == AppPage.coins || _currentPage == AppPage.coinDetail);
  }

  // Check if coin detail real-time should be active
  bool get shouldEnableCoinDetailUpdates {
    return _isAppInForeground && _currentPage == AppPage.coinDetail;
  }

  // Check if bot page updates should be active
  bool get shouldEnableBotUpdates {
    return _isAppInForeground && _currentPage == AppPage.bots;
  }

  // Check if trades page updates should be active
  bool get shouldEnableTradesUpdates {
    return _isAppInForeground && _currentPage == AppPage.trades;
  }

  // Set current page
  void setCurrentPage(AppPage page) {
    if (_currentPage != page) {
      _currentPage = page;
      _pageChangeController.add(page);
      
      // Clear coin symbol if not on coin detail page
      if (page != AppPage.coinDetail) {
        _currentCoinSymbol = null;
        _coinDetailController.add(null);
      }
      
      debugPrint('App page changed to: $page');
    }
  }

  // Set current coin for detail page
  void setCurrentCoin(String? symbol) {
    if (_currentCoinSymbol != symbol) {
      _currentCoinSymbol = symbol;
      _coinDetailController.add(symbol);
      
      if (symbol != null) {
        _currentPage = AppPage.coinDetail;
        _pageChangeController.add(AppPage.coinDetail);
      }
      
      debugPrint('Current coin changed to: $symbol');
    }
  }

  // Set app foreground state
  void setAppForegroundState(bool isInForeground) {
    if (_isAppInForeground != isInForeground) {
      _isAppInForeground = isInForeground;
      debugPrint('App foreground state changed to: $isInForeground');
    }
  }

  // Navigate to coin detail
  void navigateToCoinDetail(String symbol) {
    setCurrentCoin(symbol);
  }

  // Navigate back from coin detail
  void navigateBackFromCoinDetail() {
    setCurrentPage(AppPage.coins);
  }

  // Dispose resources
  void dispose() {
    _pageChangeController.close();
    _coinDetailController.close();
  }
}
