import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/environment.dart';

// CMS Page model
class CMSPage {
    final String id;
    final String slug;
    final String title;
    final String content;
    final bool isActive;
    final DateTime createdAt;
    final DateTime updatedAt;

    CMSPage({
      required this.id,
      required this.slug,
      required this.title,
      required this.content,
      required this.isActive,
      required this.createdAt,
      required this.updatedAt,
    });

    factory CMSPage.fromJson(Map<String, dynamic> json) {
      return CMSPage(
        id: json['id'],
        slug: json['slug'],
        title: json['title'],
        content: json['content'],
        isActive: json['is_active'] ?? true,
        createdAt: DateTime.parse(json['created_at']),
        updatedAt: DateTime.parse(json['updated_at']),
      );
    }

    Map<String, dynamic> toJson() {
      return {
        'id': id,
        'slug': slug,
        'title': title,
        'content': content,
        'is_active': isActive,
        'created_at': createdAt.toIso8601String(),
        'updated_at': updatedAt.toIso8601String(),
      };
    }
  }


class CMSService {
  static final CMSService _instance = CMSService._();
  factory CMSService() => _instance;
  CMSService._() {
    _initializeServiceRoleClient();
  }

  late final SupabaseClient _serviceRoleClient;

  // Cache for CMS pages
  final Map<String, CMSPage> _pageCache = {};
  DateTime? _lastCacheUpdate;
  static const Duration _cacheTimeout = Duration(minutes: 30);

  // Initialize service role client for bypassing RLS
  void _initializeServiceRoleClient() {
    _serviceRoleClient = SupabaseClient(
      Environment.supabaseUrl,
      Environment.supabaseServiceRoleKey,
    );
    debugPrint('🔑 CMS Service role client initialized');
  }

  // Get page by slug
  Future<CMSPage?> getPageBySlug(String slug) async {
    try {
      // Check cache first
      if (_pageCache.containsKey(slug) && _isCacheValid()) {
        debugPrint('📄 Returning cached CMS page: $slug');
        return _pageCache[slug];
      }

      debugPrint('🔍 Fetching CMS page from database: $slug');
      
      // Fetch from database using service role to bypass RLS
      final response = await _serviceRoleClient
          .from('cms_pages')
          .select()
          .eq('slug', slug)
          .eq('is_active', true)
          .maybeSingle();

      if (response == null) {
        debugPrint('❌ CMS page not found: $slug');
        return null;
      }

      final page = CMSPage.fromJson(response);
      
      // Cache the page
      _pageCache[slug] = page;
      _lastCacheUpdate = DateTime.now();
      
      debugPrint('✅ CMS page loaded: $slug');
      return page;
    } catch (e) {
      debugPrint('❌ Error fetching CMS page $slug: $e');
      return null;
    }
  }

  // Get all active pages
  Future<List<CMSPage>> getAllPages() async {
    try {
      debugPrint('🔍 Fetching all CMS pages');
      
      final response = await _serviceRoleClient
          .from('cms_pages')
          .select()
          .eq('is_active', true)
          .order('title');

      final pages = (response as List<dynamic>)
          .map((json) => CMSPage.fromJson(json))
          .toList();

      // Update cache
      for (final page in pages) {
        _pageCache[page.slug] = page;
      }
      _lastCacheUpdate = DateTime.now();

      debugPrint('✅ Loaded ${pages.length} CMS pages');
      return pages;
    } catch (e) {
      debugPrint('❌ Error fetching all CMS pages: $e');
      return [];
    }
  }

  // Check if cache is valid
  bool _isCacheValid() {
    if (_lastCacheUpdate == null) return false;
    return DateTime.now().difference(_lastCacheUpdate!) < _cacheTimeout;
  }

  // Clear cache
  void clearCache() {
    _pageCache.clear();
    _lastCacheUpdate = null;
    debugPrint('🗑️ CMS cache cleared');
  }

  // Preload common pages
  Future<void> preloadCommonPages() async {
    try {
      debugPrint('🔄 Preloading common CMS pages');
      
      final commonSlugs = ['faq', 'privacy-policy', 'terms-and-conditions', 'about-us'];
      
      for (final slug in commonSlugs) {
        await getPageBySlug(slug);
      }
      
      debugPrint('✅ Common CMS pages preloaded');
    } catch (e) {
      debugPrint('❌ Error preloading CMS pages: $e');
    }
  }

  // Create or update page (admin function)
  Future<CMSPage?> createOrUpdatePage({
    String? id,
    required String slug,
    required String title,
    required String content,
    bool isActive = true,
  }) async {
    try {
      final pageData = {
        'slug': slug,
        'title': title,
        'content': content,
        'is_active': isActive,
        'updated_at': DateTime.now().toIso8601String(),
      };

      final response = id != null
          ? await _serviceRoleClient
              .from('cms_pages')
              .update(pageData)
              .eq('id', id)
              .select()
              .single()
          : await _serviceRoleClient
              .from('cms_pages')
              .insert(pageData)
              .select()
              .single();

      final page = CMSPage.fromJson(response);
      
      // Update cache
      _pageCache[slug] = page;
      
      debugPrint('✅ CMS page ${id != null ? 'updated' : 'created'}: $slug');
      return page;
    } catch (e) {
      debugPrint('❌ Error ${id != null ? 'updating' : 'creating'} CMS page: $e');
      return null;
    }
  }

  // Delete page (admin function)
  Future<bool> deletePage(String id) async {
    try {
      await _serviceRoleClient
          .from('cms_pages')
          .delete()
          .eq('id', id);

      // Remove from cache
      _pageCache.removeWhere((key, page) => page.id == id);
      
      debugPrint('✅ CMS page deleted: $id');
      return true;
    } catch (e) {
      debugPrint('❌ Error deleting CMS page: $e');
      return false;
    }
  }

  // Search pages
  Future<List<CMSPage>> searchPages(String query) async {
    try {
      final response = await _serviceRoleClient
          .from('cms_pages')
          .select()
          .eq('is_active', true)
          .or('title.ilike.%$query%,content.ilike.%$query%')
          .order('title');

      return (response as List<dynamic>)
          .map((json) => CMSPage.fromJson(json))
          .toList();
    } catch (e) {
      debugPrint('❌ Error searching CMS pages: $e');
      return [];
    }
  }
}
