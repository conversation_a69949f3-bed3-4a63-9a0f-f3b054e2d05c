import 'dart:async';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';

class DeepLinkService {
  static const MethodChannel _channel = MethodChannel('dextrip.deeplink/channel');
  static final StreamController<Uri> _linkStreamController = StreamController<Uri>.broadcast();
  
  static Stream<Uri> get linkStream => _linkStreamController.stream;
  
  static bool _initialized = false;

  // Initialize the deep link service
  static Future<void> initialize() async {
    if (_initialized) return;

    try {
      // Set up method channel handler
      _channel.setMethodCallHandler(_handleMethodCall);

      // Get initial link if app was opened via deep link
      final String? initialLink = await _channel.invokeMethod('getInitialLink');
      if (initialLink != null) {
        final uri = Uri.parse(initialLink);
        _linkStreamController.add(uri);
      }
      
      _initialized = true;
      debugPrint('Deep link service initialized');
    } catch (e) {
      debugPrint('Error initializing deep link service: $e');
    }
  }

  // Handle method calls from native platform
  static Future<dynamic> _handleMethodCall(MethodCall call) async {
    switch (call.method) {
      case 'onNewLink':
        final String link = call.arguments;
        final uri = Uri.parse(link);
        _linkStreamController.add(uri);
        debugPrint('Received deep link: $link');
        break;
      default:
        debugPrint('Unknown method call: ${call.method}');
    }
  }

  // Listen for specific scheme links
  static StreamSubscription<Uri> listen(
    String scheme,
    Function(Uri) onLink,
  ) {
    return _linkStreamController.stream
        .where((uri) => uri.scheme == scheme)
        .listen(onLink);
  }

  // Dispose resources
  static void dispose() {
    _linkStreamController.close();
    _initialized = false;
  }
}
