import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/coin_model.dart';
import '../config/environment.dart';

class SupabaseCoinService {
  static final SupabaseCoinService _instance = SupabaseCoinService._();
  factory SupabaseCoinService() => _instance;
  SupabaseCoinService._() {
    _initializeServiceRoleClient();
  }

  final SupabaseClient _supabase = Supabase.instance.client;
  late final SupabaseClient _serviceRoleClient;
  late final SupabaseClient _realtimeServiceClient; // Service role client for real-time

  // Real-time subscriptions
  RealtimeChannel? _realtimeChannel;
  StreamSubscription<List<Map<String, dynamic>>>? _subscription;
  StreamSubscription<List<Map<String, dynamic>>>? _singleCoinSubscription;

  // Stream controller for real-time coin updates
  final StreamController<List<CoinData>> _coinStreamController =
      StreamController<List<CoinData>>.broadcast();
  final StreamController<CoinData> _singleCoinStreamController =
      StreamController<CoinData>.broadcast();

  // Cache for coins to avoid unnecessary API calls
  List<CoinData> _cachedCoins = [];
  DateTime? _lastFetch;
  static const Duration _cacheTimeout = Duration(seconds: 30);

  // Real-time subscription state
  bool _isRealTimeActive = false;
  String? _currentFilter; // Track current filter/search
  String? _singleCoinAddress; // Track single coin subscription

  // Initialize service role client for bypassing RLS
  void _initializeServiceRoleClient() {
    try {
      // Service role client for API calls
      _serviceRoleClient = SupabaseClient(
        Environment.supabaseUrl,
        Environment.supabaseServiceRoleKey,
      );
      debugPrint('🔑 Service role client initialized for self-hosted Supabase');
      debugPrint('🔑 Service role URL: ${Environment.supabaseUrl}');
      debugPrint('🔑 Service role key: ${Environment.supabaseServiceRoleKey.substring(0, 20)}...');

      // Service role client for real-time subscriptions (RLS bypass)
      _realtimeServiceClient = SupabaseClient(
        Environment.supabaseUrl,
        Environment.supabaseServiceRoleKey,
        realtimeClientOptions: const RealtimeClientOptions(
          logLevel: RealtimeLogLevel.info,
          timeout: Duration(seconds: 30),
        ),
      );
      debugPrint('🔗 Real-time service role client initialized for RLS-enabled tables');
      debugPrint('🔗 Real-time client will bypass RLS for WebSocket subscriptions');
    } catch (e) {
      debugPrint('❌ Failed to initialize service role clients: $e');
    }
  }

  // Get stream of coin updates
  Stream<List<CoinData>> get coinStream => _coinStreamController.stream;

  // Get stream of single coin updates
  Stream<CoinData> get singleCoinStream => _singleCoinStreamController.stream;

  // Get cached coins
  List<CoinData> get cachedCoins => _cachedCoins;

  // Check if real-time is active
  bool get isRealTimeActive => _isRealTimeActive;

  // Stop real-time subscription
  Future<void> stopRealTimeSubscription() async {
    debugPrint('⏹️ Stopping real-time subscription...');
    _isRealTimeActive = false;

    await _realtimeChannel?.unsubscribe();
    await _subscription?.cancel();

    _realtimeChannel = null;
    _subscription = null;

    debugPrint('✅ Real-time subscription stopped');
    debugPrint('✅ Service role real-time client cleaned up');
  }

  // Initialize real-time subscription with WebSocket fallback to ultra-fast polling
  Future<void> initializeRealTimeSubscription({
    String? searchFilter,
    List<String>? visibleCoinIds,
  }) async {
    try {
      debugPrint('🚀 Initializing Supabase real-time system...');
      debugPrint('🏠 Self-hosted Supabase URL: ${Environment.supabaseUrl}');
      debugPrint('🔗 WebSocket URL: ${Environment.supabaseUrl}/realtime/v1/websocket');
      debugPrint('⚡ Attempting WebSocket first, fallback to ultra-fast polling');

      // Cancel existing subscriptions
      await _realtimeChannel?.unsubscribe();
      await _subscription?.cancel();

      _currentFilter = searchFilter;

      // Load initial data
      debugPrint('📊 Loading initial coin data...');
      await _loadInitialData();

      // Set up ONLY WebSocket real-time subscription (no polling)
      debugPrint('🔗 Setting up WebSocket real-time subscription...');
      await _setupRealtimeChannel();

    } catch (e) {
      debugPrint('❌ Error initializing real-time system: $e');
      debugPrint('🔄 Falling back to ultra-fast polling for real-time experience');
      _startOptimizedPolling(visibleCoinIds);
    }
  }

  // Handle real-time coin insertion
  void _handleCoinInsert(Map<String, dynamic> newCoin) {
    try {
      final coinData = _convertPoolToCoinData(newCoin);

      // Add to cache at the beginning (newest first)
      _cachedCoins.insert(0, coinData);

      // Emit updated list
      _coinStreamController.add(List.from(_cachedCoins));

      debugPrint('🆕 New coin added to list: ${coinData.name} (${coinData.symbol}) - ${coinData.formattedPrice}');
      debugPrint('📊 Total coins: ${_cachedCoins.length}');

    } catch (e) {
      debugPrint('❌ Error handling coin insert: $e');
    }
  }

  // Handle real-time coin update
  void _handleCoinUpdate(Map<String, dynamic> newCoin, Map<String, dynamic> oldCoin) {
    try {
      final coinData = _convertPoolToCoinData(newCoin);
      final coinId = coinData.id;

      // Find and update the coin in cache
      final index = _cachedCoins.indexWhere((coin) => coin.id == coinId);
      if (index != -1) {
        _cachedCoins[index] = coinData;

        // Emit updated list
        _coinStreamController.add(List.from(_cachedCoins));

        debugPrint('📝 Coin updated: ${coinData.name} (${coinData.symbol}) - ${coinData.formattedPrice}');
      } else {
        debugPrint('⚠️ Updated coin not found in cache: $coinId');
      }

    } catch (e) {
      debugPrint('❌ Error handling coin update: $e');
    }
  }

  // Handle real-time coin deletion
  void _handleCoinDelete(Map<String, dynamic> deletedCoin) {
    try {
      final coinId = deletedCoin['id']?.toString();
      if (coinId == null) return;

      // Remove from cache
      _cachedCoins.removeWhere((coin) => coin.id == coinId);

      // Emit updated list
      _coinStreamController.add(List.from(_cachedCoins));

      debugPrint('🗑️ Coin deleted: ${deletedCoin['name']} (${deletedCoin['symbol']})');
      debugPrint('📊 Total coins: ${_cachedCoins.length}');

    } catch (e) {
      debugPrint('❌ Error handling coin delete: $e');
    }
  }

  // Load initial data before setting up real-time subscription
  Future<void> _loadInitialData() async {
    try {
      debugPrint('📊 Loading initial coin data...');

      // Use the authenticated client for RLS compliance
      final response = await _supabase
          .from('tokens')
          .select()
          .order('created_at', ascending: false)
          .limit(100);

      if (response.isNotEmpty) {
        final coins = response.map((pool) => _convertPoolToCoinData(pool)).toList();

        // Update cache and emit initial data
        _cachedCoins = coins;
        _lastFetch = DateTime.now();
        _coinStreamController.add(coins);

        debugPrint('✅ Initial data loaded: ${coins.length} coins');
        debugPrint('💰 Sample coin: ${coins.first.name} (${coins.first.symbol})');
      } else {
        debugPrint('⚠️ No initial data found in tokens table');
      }
    } catch (e) {
      debugPrint('❌ Failed to load initial data: $e');
      // Try with service role as fallback
      try {
        debugPrint('🔄 Trying with service role client...');
        final response = await _serviceRoleClient
            .from('tokens')
            .select()
            .order('created_at', ascending: false)
            .limit(100);

        if (response.isNotEmpty) {
          final coins = response.map((pool) => _convertPoolToCoinData(pool)).toList();
          _cachedCoins = coins;
          _lastFetch = DateTime.now();
          _coinStreamController.add(coins);
          debugPrint('✅ Initial data loaded with service role: ${coins.length} coins');
        }
      } catch (serviceError) {
        debugPrint('❌ Service role also failed: $serviceError');
      }
    }
  }

  // Set up real-time channel subscription using Supabase WebSocket channels
  Future<void> _setupRealtimeChannel() async {
    try {
      debugPrint('🔗 Setting up Supabase real-time WebSocket channel for RLS-enabled tokens table...');
      debugPrint('📡 WebSocket endpoint: ${Environment.supabaseUrl}/realtime/v1/websocket');
      debugPrint('🔧 Apache proxy: /realtime/v1/websocket -> ws://localhost:4000/socket/websocket');
      debugPrint('🔑 Using service role client to bypass RLS for real-time subscriptions');

      // Create a real-time channel using service role client (bypasses RLS)
      _realtimeChannel = _realtimeServiceClient.channel('tokens_realtime_channel');

      debugPrint('📺 Channel created: tokens_realtime_channel');
      debugPrint('🎯 Subscribing to PostgreSQL changes on public.tokens table...');

      // Subscribe to all changes on the tokens table
      _realtimeChannel!
          .onPostgresChanges(
            event: PostgresChangeEvent.all,
            schema: 'public',
            table: 'tokens',
            callback: (payload) {
              final now = DateTime.now();
              final timeString = '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')}';

              debugPrint('🔥 [$timeString] REAL-TIME WEBSOCKET EVENT RECEIVED!');
              debugPrint('📊 [$timeString] Event type: ${payload.eventType}');
              debugPrint('📊 [$timeString] Table: ${payload.table}');
              debugPrint('📊 [$timeString] Schema: ${payload.schema}');

              // Handle different types of changes
              switch (payload.eventType) {
                case PostgresChangeEvent.insert:
                  debugPrint('🆕 [$timeString] NEW COIN INSERTED VIA WEBSOCKET!');
                  debugPrint('🆕 [$timeString] Coin: ${payload.newRecord?['name']} (${payload.newRecord?['symbol']})');
                  _handleCoinInsert(payload.newRecord!);
                  break;
                case PostgresChangeEvent.update:
                  debugPrint('📝 [$timeString] COIN UPDATED VIA WEBSOCKET!');
                  debugPrint('📝 [$timeString] Coin: ${payload.newRecord?['name']} (${payload.newRecord?['symbol']})');
                  _handleCoinUpdate(payload.newRecord!, payload.oldRecord!);
                  break;
                case PostgresChangeEvent.delete:
                  debugPrint('🗑️ [$timeString] COIN DELETED VIA WEBSOCKET!');
                  debugPrint('🗑️ [$timeString] Coin: ${payload.oldRecord?['name']} (${payload.oldRecord?['symbol']})');
                  _handleCoinDelete(payload.oldRecord!);
                  break;
                case PostgresChangeEvent.all:
                  debugPrint('🔄 [$timeString] ALL CHANGES EVENT (generic)');
                  break;
              }
            },
          )
          .subscribe((status, [error]) {
            final now = DateTime.now();
            final timeString = '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')}';

            if (status == RealtimeSubscribeStatus.subscribed) {
              debugPrint('✅ [$timeString] REAL-TIME WEBSOCKET CHANNEL SUBSCRIBED SUCCESSFULLY!');
              debugPrint('🎉 [$timeString] WebSocket real-time is now active for tokens table');
              debugPrint('🔄 [$timeString] Any database changes will be reflected instantly');
              _isRealTimeActive = true;
            } else if (status == RealtimeSubscribeStatus.channelError) {
              debugPrint('❌ [$timeString] Real-time WebSocket channel error: $error');
              debugPrint('🔧 [$timeString] Check Apache configuration: /realtime/v1/websocket proxy');
              debugPrint('🔧 [$timeString] Ensure Supabase real-time server is running on port 4000');
              debugPrint('🔧 [$timeString] Restart Apache after configuration changes');
              _isRealTimeActive = false;
            } else if (status == RealtimeSubscribeStatus.timedOut) {
              debugPrint('⏰ [$timeString] Real-time WebSocket subscription timed out');
              debugPrint('🔧 [$timeString] Check network connectivity and WebSocket server status');
              _isRealTimeActive = false;
            } else if (status == RealtimeSubscribeStatus.closed) {
              debugPrint('🔚 [$timeString] Real-time WebSocket channel closed');
              _isRealTimeActive = false;
            }
          });

    } catch (e) {
      debugPrint('❌ Failed to set up real-time WebSocket channel: $e');
      _isRealTimeActive = false;
    }
  }

  // Initialize real-time subscription for a single coin
  Future<void> initializeSingleCoinSubscription(String tokenAddress) async {
    try {
      // Cancel existing single coin subscription
      await _singleCoinSubscription?.cancel();

      _singleCoinAddress = tokenAddress;

      debugPrint('🎯 Setting up single coin subscription for: $tokenAddress');

      _singleCoinSubscription = _supabase
          .from('tokens')
          .stream(primaryKey: ['id'])
          .eq('address', tokenAddress)
          .listen(
        (List<Map<String, dynamic>> data) {
          if (data.isNotEmpty) {
            final coinData = _convertPoolToCoinData(data.first);
            debugPrint('💰 Single coin update: ${coinData.symbol} - ${coinData.tokenAddress}');
            _singleCoinStreamController.add(coinData);
          }
        },
        onError: (error) {
          debugPrint('❌ Single coin subscription error: $error');
        },
      );

      debugPrint('✅ Single coin subscription initialized for $tokenAddress');
    } catch (e) {
      debugPrint('❌ Error initializing single coin subscription: $e');
    }
  }

  // Cancel single coin subscription
  Future<void> cancelSingleCoinSubscription() async {
    try {
      await _singleCoinSubscription?.cancel();
      _singleCoinSubscription = null;
      _singleCoinAddress = null;
      debugPrint('🛑 Single coin subscription cancelled');
    } catch (e) {
      debugPrint('❌ Error cancelling single coin subscription: $e');
    }
  }

  // Pause real-time subscription to save bandwidth
  Future<void> pauseRealTimeSubscription() async {
    try {
      _isRealTimeActive = false;
      await _subscription?.cancel();
      _subscription = null;
      _currentFilter = null;
      debugPrint('⏸️ Real-time subscription paused');
    } catch (e) {
      debugPrint('❌ Error pausing real-time subscription: $e');
    }
  }

  // Resume real-time subscription
  Future<void> resumeRealTimeSubscription() async {
    if (!_isRealTimeActive) {
      await initializeRealTimeSubscription();
    }
  }

  // Handle real-time updates
  void _handleRealTimeUpdate(List<Map<String, dynamic>> data, String? searchFilter, List<String>? visibleCoinIds) {
    try {
      final now = DateTime.now();
      final timeString = '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')}';

      debugPrint('🔄 [$timeString] Processing real-time update with ${data.length} records');

      var coins = data.map((pool) => _convertPoolToCoinData(pool)).toList();

      // Apply client-side filtering if needed
      if (searchFilter != null && searchFilter.isNotEmpty) {
        final originalCount = coins.length;
        coins = coins.where((coin) {
          return coin.name.toLowerCase().contains(searchFilter.toLowerCase()) ||
                 coin.symbol.toLowerCase().contains(searchFilter.toLowerCase()) ||
                 (coin.tokenAddress?.toLowerCase().contains(searchFilter.toLowerCase()) ?? false);
        }).toList();
        debugPrint('🔍 Filtered ${originalCount} coins to ${coins.length} based on search: "$searchFilter"');
      }

      // Apply visibility filtering if needed
      if (visibleCoinIds != null && visibleCoinIds.isNotEmpty) {
        final originalCount = coins.length;
        coins = coins.where((coin) => visibleCoinIds.contains(coin.id)).toList();
        debugPrint('👁️ Filtered ${originalCount} coins to ${coins.length} based on visibility');
      }

      // Log sample data for debugging (only first time)
      if (data.isNotEmpty && _lastFetch == null) {
        final samplePool = data.first;
        debugPrint('📊 Sample pool data: ${samplePool.keys.toList()}');
        debugPrint('💰 Available value fields: fdv_usd=${samplePool['fdv_usd']}, liquidity=${samplePool['liquidity']}, market_cap=${samplePool['market_cap']}');
        debugPrint('📈 Change fields: market_cap_change_percent=${samplePool['market_cap_change_percent']}');
      }

      // Update cache
      _cachedCoins = coins;
      _lastFetch = DateTime.now();

      // Emit to stream
      _coinStreamController.add(coins);

      // Log detailed update information
      if (coins.isNotEmpty) {
        final sampleCoin = coins.first;
        debugPrint('✅ [$timeString] Real-time update: ${coins.length} coins processed and emitted');
        debugPrint('💰 [$timeString] Sample coin: ${sampleCoin.name} (${sampleCoin.symbol}) - Price: ${sampleCoin.formattedPrice}');
        debugPrint('📊 [$timeString] Real-time subscription active and working');
        debugPrint('🔄 [$timeString] Coins emitted to stream for UI update');
      } else {
        debugPrint('⚠️ [$timeString] Real-time update: No coins after filtering');
      }
    } catch (e) {
      debugPrint('❌ Error handling real-time update: $e');
    }
  }

  // Convert Supabase pool data to CoinData
  CoinData _convertPoolToCoinData(Map<String, dynamic> pool) {
    // Try different fields for price data since market_cap is null
    final currentPrice = _parseDouble(pool['fdv_usd']) ??
                        _parseDouble(pool['liquidity']) ??
                        _parseDouble(pool['market_cap']);

    final marketCap = _parseDouble(pool['market_cap']) ??
                     _parseDouble(pool['fdv_usd']);

    final tokenAddress = pool['token_address'] ?? pool['address'] ?? pool['mint'];

    //debugPrint('🪙 Converting pool data: ${pool['symbol']} - Price: ${CurrencyFormatter.format(currentPrice)} (fdv: ${pool['fdv_usd']}, liq: ${pool['liquidity']}, mc: ${pool['market_cap']}), Token: $tokenAddress');

    return CoinData(
      id: pool['token_address'] ?? pool['address'] ?? pool['id']?.toString() ?? '',
      symbol: _sanitizeString(pool['symbol']) ?? 'UNKNOWN',
      name: _sanitizeString(pool['name']) ?? '',
      image: pool['image_url'],
      tokenAddress: tokenAddress,
      currentPrice: currentPrice,
      marketCap: marketCap,
      marketCapRank: null, // Not available in your table structure
      fullyDilutedValuation: _parseDouble(pool['fdv_usd']),
      totalVolume: _parseDouble(pool['liquidity']), // Using liquidity as volume proxy
      high24h: null, // Not available in your table structure
      low24h: null, // Not available in your table structure
      priceChange24h: null, // Not available in your table structure
      priceChangePercentage24h: null, // Not available in your table structure
      marketCapChange24h: null, // Not available in your table structure
      marketCapChangePercentage24h: null, // Not available in your table structure
      circulatingSupply: null, // Not available in your table structure
      totalSupply: null, // Not available in your table structure
      maxSupply: null, // Not available in your table structure
      ath: null, // Not available in your table structure
      athChangePercentage: null, // Not available in your table structure
      athDate: null, // Not available in your table structure
      atl: null, // Not available in your table structure
      atlChangePercentage: null, // Not available in your table structure
      atlDate: null, // Not available in your table structure
      market_cap_change_percent: _parseDouble(pool['market_cap_change_percent']),
      lastUpdated: pool['created_at'],
    );
  }

  // Helper method to safely parse double values
  double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value);
    }
    return null;
  }

  // Helper method to sanitize strings and remove problematic characters
  String _sanitizeString(String? input) {
    if (input == null) return '';

    // Replace problematic Unicode characters with safe alternatives
    return input
        .replaceAll(RegExp(r'[^\x00-\x7F]'), '') // Remove non-ASCII characters
        .replaceAll(RegExp(r'[^\w\s\-\.]'), '') // Keep only alphanumeric, spaces, hyphens, dots
        .trim();
  }



  // Get initial coin data (non-real-time)
  Future<List<CoinData>> getCoins({
    int limit = 50,
    int offset = 0,
    String? searchQuery,
  }) async {
    try {
      debugPrint('🔍 Getting coins: limit=$limit, offset=$offset, search=$searchQuery');

      final now = DateTime.now();

      final shouldUseCache = _cachedCoins.isNotEmpty &&
          _lastFetch != null &&
          now.difference(_lastFetch!) < _cacheTimeout &&
          searchQuery == null;

      if (shouldUseCache) {
        debugPrint('📦 Using cached coins: ${_cachedCoins.length}');
        return _cachedCoins.skip(offset).take(limit).toList();
      }

      List<Map<String, dynamic>> rawResults = [];

      // Test direct table access first
      debugPrint('🔍 Testing direct table access...');

      if (searchQuery != null && searchQuery.isNotEmpty) {
        final queryText = searchQuery.toLowerCase();

        final searchLimit = limit * 2; // Get more results for better search coverage

        debugPrint('🔍 Using service role for search queries...');
        final results = await Future.wait([
          _serviceRoleClient
              .from('tokens')
              .select()
              .ilike('name', '%$queryText%')
              .order('created_at', ascending: false)
              .limit(searchLimit),
          _serviceRoleClient
              .from('tokens')
              .select()
              .ilike('symbol', '%$queryText%')
              .order('created_at', ascending: false)
              .limit(searchLimit),
          _serviceRoleClient
              .from('tokens')
              .select()
              .ilike('address', '%$queryText%')
              .order('created_at', ascending: false)
              .limit(searchLimit),
          _serviceRoleClient
              .from('tokens')
              .select()
              .ilike('address', '%$queryText%')
              .order('created_at', ascending: false)
              .limit(searchLimit),
        ]);

        final merged = results.expand((e) => e).toList();

        // Remove duplicates by ID
        final unique = {
          for (var item in merged) item['id']: item
        }.values.toList();

        // Sort by created_at descending
        unique.sort((a, b) => b['created_at'].compareTo(a['created_at']));

        rawResults = unique.skip(offset).take(limit).toList();
      } else {
        debugPrint('🔍 Querying tokens table with service role...');
        try {
          final response = await _serviceRoleClient
              .from('tokens')
              .select()
              .order('created_at', ascending: false)
              .range(offset, offset + limit - 1)
              .limit(limit);

          debugPrint('✅ Service role query successful: ${response.length} records');
          if (response.isNotEmpty) {
            debugPrint('📊 Sample token: ${response.first.keys.toList()}');
          }

          rawResults = List<Map<String, dynamic>>.from(response);
        } catch (e) {
          debugPrint('❌ Service role query failed: $e');
          // Try simple query without range
          debugPrint('🔍 Trying simple service role query...');
          try {
            final simpleResponse = await _serviceRoleClient
                .from('tokens')
                .select('*')
                .limit(limit);

            debugPrint('✅ Simple service role query: ${simpleResponse.length} records');
            rawResults = List<Map<String, dynamic>>.from(simpleResponse);
          } catch (e2) {
            debugPrint('❌ Simple service role query also failed: $e2');
            throw e2;
          }
        }
      }

      final coins = rawResults.map(_convertPoolToCoinData).toList();

      if (offset == 0 && searchQuery == null) {
        _cachedCoins = coins;
        _lastFetch = now;
      }

      return coins;
    } catch (e) {
      debugPrint('Error fetching coins from Supabase: $e');
      throw Exception('Failed to fetch coins: $e');
    }
  }

  // Search coins by query
  Future<List<CoinData>> searchCoins(String query) async {
    if (query.isEmpty) {
      return getCoins();
    }

    try {
      // Check if query looks like a token address
      if (_isTokenAddress(query)) {
        return await _searchByTokenAddress(query);
      }

      // Search by name, symbol, or partial address
      return await getCoins(searchQuery: query, limit: 100);
    } catch (e) {
      debugPrint('Error searching coins: $e');
      throw Exception('Search failed: $e');
    }
  }

  // Search by specific token address
  Future<List<CoinData>> _searchByTokenAddress(String address) async {
    try {
      final response = await _supabase
          .from('tokens')
          .select()
          .or('token_address.eq.$address,address.eq.$address')
          .limit(1);

      if (response.isNotEmpty) {
        return [_convertPoolToCoinData(response.first)];
      }

      return [];
    } catch (e) {
      debugPrint('Error searching by token address: $e');
      return [];
    }
  }

  // Check if query looks like a token address
  bool _isTokenAddress(String query) {
    // Solana address (base58, 32-44 chars)
    final solanaRegex = RegExp(r'^[1-9A-HJ-NP-Za-km-z]{32,44}$');
    return solanaRegex.hasMatch(query) && !query.startsWith('0x');
  }

  // Get trending/top coins (most recent or highest volume)
  Future<List<CoinData>> getTrendingCoins({
    int page = 1,
    int perPage = 25,
    String orderBy = 'created_at', // 'created_at', 'market_cap', 'liquidity'
    bool ascending = false,
  }) async {
    try {
      debugPrint('🔍 Getting trending coins: page=$page, perPage=$perPage, orderBy=$orderBy');

      final offset = (page - 1) * perPage;

      // Try simple query with service role
      debugPrint('🔍 Trying service role query...');
      final response = await _serviceRoleClient
          .from('tokens')
          .select('*')
          .order(orderBy, ascending: ascending)
          .limit(perPage);

      debugPrint('✅ Trending coins query successful: ${response.length} records');

      if (response.isNotEmpty) {
        debugPrint('📊 Sample trending coin: ${response.first.keys.toList()}');
      }

      final coins = (response as List<dynamic>)
          .map((pool) => _convertPoolToCoinData(pool))
          .toList();

      debugPrint('✅ Converted ${coins.length} trending coins');
      return coins;
    } catch (e) {
      debugPrint('❌ Error fetching trending coins: $e');

      // Try fallback with getCoins
      debugPrint('🔄 Trying fallback with getCoins...');
      try {
        return await getCoins(limit: perPage, offset: (page - 1) * perPage);
      } catch (e2) {
        debugPrint('❌ Fallback also failed: $e2');
        throw Exception('Failed to fetch trending coins: $e');
      }
    }
  }

  // Get coins with filters (based on your actual table structure)
  Future<List<CoinData>> getFilteredCoins({
    double? minMarketCap,
    double? maxMarketCap,
    double? minLiquidity,
    double? maxLiquidity,
    double? minFdv,
    double? maxFdv,
    String? dexId,
    int limit = 50,
    int offset = 0,
  }) async {
    try {
      var query = _supabase
          .from('tokens')
          .select();

      if (minMarketCap != null) {
        query = query.gte('market_cap', minMarketCap);
      }
      if (maxMarketCap != null) {
        query = query.lte('market_cap', maxMarketCap);
      }
      if (minLiquidity != null) {
        query = query.gte('liquidity', minLiquidity);
      }
      if (maxLiquidity != null) {
        query = query.lte('liquidity', maxLiquidity);
      }
      if (minFdv != null) {
        query = query.gte('fdv_usd', minFdv);
      }
      if (maxFdv != null) {
        query = query.lte('fdv_usd', maxFdv);
      }
      if (dexId != null && dexId.isNotEmpty) {
        query = query.eq('dex_id', dexId);
      }

      final response = await query
          .order('created_at', ascending: false)
          .range(offset, offset + limit - 1)
          .limit(limit);

      final coins = (response as List<dynamic>)
          .map((pool) => _convertPoolToCoinData(pool))
          .toList();

      return coins;
    } catch (e) {
      debugPrint('Error fetching filtered coins: $e');
      throw Exception('Failed to fetch filtered coins: $e');
    }
  }

  // Get coin by token address
  Future<CoinData?> getCoinByAddress(String tokenAddress) async {
    try {
      final response = await _supabase
          .from('tokens')
          .select()
          .or('token_address.eq.$tokenAddress,address.eq.$tokenAddress')
          .limit(1);

      if (response.isNotEmpty) {
        return _convertPoolToCoinData(response.first);
      }

      return null;
    } catch (e) {
      debugPrint('Error fetching coin by address: $e');
      return null;
    }
  }

  // Periodic polling fallback
  Timer? _pollingTimer;

  void _startPeriodicPolling() {
    _pollingTimer?.cancel();
    _pollingTimer = Timer.periodic(const Duration(seconds: 30), (timer) async {
      if (!_isRealTimeActive) {
        timer.cancel();
        return;
      }

      try {
        debugPrint('🔄 Periodic polling for coins...');
        final coins = await getCoins(limit: 50);
        if (coins.isNotEmpty) {
          _coinStreamController.add(coins);
          debugPrint('✅ Periodic polling: ${coins.length} coins updated');
        }
      } catch (e) {
        debugPrint('❌ Periodic polling failed: $e');
      }
    });
  }

  void _startOptimizedPolling(List<String>? visibleCoinIds) {
    _pollingTimer?.cancel();

    // Use extremely fast intervals for true real-time experience
    final interval = visibleCoinIds != null && visibleCoinIds.isNotEmpty
        ? const Duration(seconds: 1)  // 1-second updates for visible coins (true real-time)
        : const Duration(seconds: 2); // 2-second general updates (near real-time)

    debugPrint('🚀 Starting REAL-TIME polling system for self-hosted Supabase');
    debugPrint('⚡ Polling interval: ${interval.inSeconds}s (TRUE REAL-TIME experience)');
    debugPrint('🏠 Self-hosted Supabase: Ultra-fast polling = instant real-time updates');
    debugPrint('💡 WebSocket fallback: Polling provides better reliability than WebSocket');
    if (visibleCoinIds != null && visibleCoinIds.isNotEmpty) {
      debugPrint('👁️ Polling ${visibleCoinIds.length} visible coins: ${visibleCoinIds.take(3).join(", ")}${visibleCoinIds.length > 3 ? "..." : ""}');
    }

    // Set real-time as active for polling
    _isRealTimeActive = true;

    // Start immediate polling
    debugPrint('🚀 Starting immediate real-time polling...');
    _performPollingUpdate(visibleCoinIds);

    _pollingTimer = Timer.periodic(interval, (timer) async {
      if (!_isRealTimeActive) {
        debugPrint('⏹️ Stopping real-time polling - inactive');
        timer.cancel();
        return;
      }

      _performPollingUpdate(visibleCoinIds);
    });
  }

  void _performPollingUpdate(List<String>? visibleCoinIds) async {
    final now = DateTime.now();
    final timeString = '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')}';

    try {
      List<CoinData> coins;

      if (visibleCoinIds != null && visibleCoinIds.isNotEmpty) {
        debugPrint('🎯 [$timeString] Polling ${visibleCoinIds.length} visible coins...');
        coins = await getCoinsById(visibleCoinIds);
      } else {
        debugPrint('🔄 [$timeString] General polling for latest coins...');
        coins = await getCoins(limit: 100);
      }

      if (coins.isNotEmpty) {
        // Check for new coins (real-time simulation)
        final previousCount = _cachedCoins.length;
        final newCoins = coins.where((coin) =>
          !_cachedCoins.any((cached) => cached.id == coin.id)
        ).toList();

        if (newCoins.isNotEmpty) {
          debugPrint('🆕 [$timeString] NEW COINS DETECTED: ${newCoins.length} new coins added!');
          for (final newCoin in newCoins.take(3)) {
            debugPrint('🆕 [$timeString] New coin: ${newCoin.name} (${newCoin.symbol}) - ${newCoin.formattedPrice}');
          }
        }

        // Update cache
        _cachedCoins = coins;
        _lastFetch = DateTime.now();

        // Emit to stream
        _coinStreamController.add(coins);
        final sampleCoin = coins.first;
        final changeCount = coins.length - previousCount;
        final changeText = changeCount > 0 ? '+$changeCount' : changeCount == 0 ? '0' : '$changeCount';
        debugPrint('✅ [$timeString] Polling update: ${coins.length} coins ($changeText change) | Sample: ${sampleCoin.name} - ${sampleCoin.formattedPrice}');
        debugPrint('📊 [$timeString] Self-hosted real-time simulation: Cache updated with ${coins.length} coins');
      } else {
        debugPrint('⚠️ [$timeString] Polling returned no coins - checking self-hosted configuration');
      }
    } catch (e) {
      debugPrint('❌ [$timeString] Optimized polling failed: $e');
      // Try with anon client if service role fails
      try {
        debugPrint('🔄 [$timeString] Retrying with anon client...');
        final anonCoins = await _getCoinsWithAnonClient();
        if (anonCoins.isNotEmpty) {
          _cachedCoins = anonCoins;
          _lastFetch = DateTime.now();
          _coinStreamController.add(anonCoins);
          debugPrint('✅ [$timeString] Anon client success: ${anonCoins.length} coins');
        }
      } catch (anonError) {
        debugPrint('❌ [$timeString] Anon client also failed: $anonError');
      }
    }
  }

  // Get specific coins by their IDs for visibility-based updates
  Future<List<CoinData>> getCoinsById(List<String> coinIds) async {
    try {
      debugPrint('🎯 Fetching ${coinIds.length} specific coins by ID...');

      final response = await _serviceRoleClient
          .from('tokens')
          .select('*')
          .inFilter('id', coinIds)
          .order('created_at', ascending: false);

      final coins = response.map<CoinData>((json) => CoinData.fromJson(json)).toList();

      debugPrint('✅ Retrieved ${coins.length} coins by ID');
      return coins;
    } catch (e) {
      debugPrint('❌ Error fetching coins by ID: $e');
      return [];
    }
  }

  // Fallback method using anon client for RLS issues
  Future<List<CoinData>> _getCoinsWithAnonClient() async {
    try {
      debugPrint('🔄 Trying anon client for RLS compatibility...');

      final response = await _supabase
          .from('tokens')
          .select()
          .order('created_at', ascending: false)
          .limit(50);

      if (response.isEmpty) {
        debugPrint('⚠️ Anon client returned empty response');
        return [];
      }

      final coins = response.map((pool) => _convertPoolToCoinData(pool)).toList();
      debugPrint('✅ Anon client success: ${coins.length} coins retrieved');
      return coins;
    } catch (e) {
      debugPrint('❌ Anon client failed: $e');
      return [];
    }
  }

  // Dispose resources
  void dispose() {
    _isRealTimeActive = false;
    _pollingTimer?.cancel();
    _subscription?.cancel();
    _singleCoinSubscription?.cancel();
    _realtimeChannel?.unsubscribe();
    _coinStreamController.close();
    _singleCoinStreamController.close();
    _currentFilter = null;
    _singleCoinAddress = null;
  }

  // Refresh cache
  Future<void> refreshCache() async {
    try {
      _cachedCoins.clear();
      _lastFetch = null;
      await getCoins();
    } catch (e) {
      debugPrint('Error refreshing cache: $e');
    }
  }

  // Get connection status
  bool get isConnected => _subscription != null;

  // Get cache status
  bool get hasCachedData => _cachedCoins.isNotEmpty;

  // Get last update time
  DateTime? get lastUpdateTime => _lastFetch;
}
