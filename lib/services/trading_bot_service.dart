import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/trading_bot.dart';
import '../models/coin_model.dart';
import 'supabase_auth_service.dart';

class TradingBotService {
  static final TradingBotService _instance = TradingBotService._();
  factory TradingBotService() => _instance;
  TradingBotService._();

  final SupabaseClient _supabase = Supabase.instance.client;
  final SupabaseAuthService _authService = SupabaseAuthService();

  /// Test database connection and schema
  Future<bool> testDatabaseConnection() async {
    try {
      debugPrint('🔍 Testing database connection and schema...');

      // Test basic connection
      await _supabase.from('trading_bots').select('count').limit(1);
      debugPrint('✅ Database connection successful');

      // Test if we can query the table structure (this will help identify missing columns)
      try {
        await _supabase
            .from('trading_bots')
            .select(
              'id, user_id, name, coin_id, coin_symbol, coin_name, coin_image, strategy, investment_amount, stop_loss, take_profit, indicators, is_active, start_immediately, risk_percentage, trading_24_7, trading_start_time, trading_end_time, created_at, updated_at',
            )
            .limit(1);
        debugPrint('✅ All required columns exist in database');
        return true;
      } catch (schemaError) {
        debugPrint('❌ Schema test failed - missing columns: $schemaError');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Database connection test failed: $e');
      return false;
    }
  }

  /// Create a new trading bot
  Future<Map<String, dynamic>?> createTradingBot({
    required String name,
    required CoinData coin,
    required String strategy,
    required double investmentAmount,
    required double stopLoss,
    required double takeProfit,
    required List<TradingIndicator> indicators,
    required bool startImmediately,
    double riskPercentage = 2.0,
    bool trading24_7 = true,
    String? tradingStartTime,
    String? tradingEndTime,
  }) async {
    try {
      debugPrint('🚀 Starting trading bot creation process...');

      // Get current user
      final userId = _authService.userId;
      debugPrint('👤 Current user ID: $userId');

      if (userId == null) {
        debugPrint('❌ User not authenticated - userId is null');
        throw Exception('User not authenticated');
      }

      debugPrint('🤖 Creating trading bot for user: $userId');
      debugPrint('📊 Bot details: $name, ${coin.symbol}, $strategy');
      debugPrint(
        '💰 Investment: $investmentAmount, Stop Loss: $stopLoss, Take Profit: $takeProfit',
      );
      debugPrint(
        '🎯 Start Immediately: $startImmediately, Risk: $riskPercentage%',
      );
      debugPrint('📈 Indicators count: ${indicators.length}');

      // Prepare indicators as JSONB
      final indicatorsJson = indicators
          .map(
            (indicator) => {
              'type': indicator.type.name,
              'name': indicator.name,
              'parameters': indicator.parameters,
              'buyThreshold': indicator.buyThreshold,
              'sellThreshold': indicator.sellThreshold,
              'isEnabled': indicator.isEnabled,
            },
          )
          .toList();

      debugPrint('📊 Indicators JSON: $indicatorsJson');

      // Prepare bot data for database
      final botData = {
        'user_id': userId,
        'name': name,
        'coin_id': coin.id,
        'coin_symbol': coin.symbol,
        'coin_name': coin.name,
        'coin_image': coin.image,
        'strategy': strategy,
        'investment_amount': investmentAmount,
        'stop_loss': stopLoss,
        'take_profit': takeProfit,
        'indicators': indicatorsJson,
        'is_active': startImmediately,
        'start_immediately': startImmediately,
        'risk_percentage': riskPercentage,
        'trading_24_7': trading24_7,
        'trading_start_time': tradingStartTime,
        'trading_end_time': tradingEndTime,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      debugPrint('💾 Prepared bot data for insertion:');
      botData.forEach((key, value) {
        debugPrint('  $key: $value (${value.runtimeType})');
      });

      debugPrint('🔗 Attempting database insertion...');

      // Test database connection first
      try {
        final testQuery = await _supabase
            .from('trading_bots')
            .select('count')
            .limit(1);
        debugPrint('✅ Database connection test successful: $testQuery');
      } catch (testError) {
        debugPrint('❌ Database connection test failed: $testError');
        throw Exception('Database connection failed: $testError');
      }

      // Insert into database
      final response = await _supabase
          .from('trading_bots')
          .insert(botData)
          .select()
          .single();

      debugPrint('✅ Trading bot created successfully!');
      debugPrint('📋 Response: $response');
      debugPrint('🆔 Bot ID: ${response['id']}');
      debugPrint('🎯 Bot name: ${response['name']}');
      debugPrint('💰 Investment: \$${response['investment_amount']}');
      debugPrint('🚀 Active: ${response['is_active']}');

      return response;
    } catch (e, stackTrace) {
      debugPrint('❌ Error creating trading bot: $e');
      debugPrint('📍 Stack trace: $stackTrace');

      // Provide more specific error information
      if (e.toString().contains('permission')) {
        debugPrint('🔒 Permission error - check RLS policies');
      } else if (e.toString().contains('column')) {
        debugPrint('📊 Column error - check database schema');
      } else if (e.toString().contains('constraint')) {
        debugPrint('⚠️ Constraint error - check data validation');
      }

      rethrow;
    }
  }

  /// Get all trading bots for current user
  Future<List<Map<String, dynamic>>> getUserTradingBots() async {
    try {
      final userId = _authService.userId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      debugPrint('📋 Fetching trading bots for user: $userId');

      final response = await _supabase
          .from('trading_bots')
          .select()
          .eq('user_id', userId)
          .eq('is_archived', false)
          .order('created_at', ascending: false);

      debugPrint('✅ Retrieved ${response.length} trading bots');
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('❌ Error fetching trading bots: $e');
      rethrow;
    }
  }

  /// Update trading bot status (start/stop)
  Future<bool> updateBotStatus(String botId, bool isActive) async {
    try {
      final userId = _authService.userId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      debugPrint('🔄 Updating bot status: $botId -> $isActive');

      await _supabase
          .from('trading_bots')
          .update({
            'is_active': isActive,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', botId)
          .eq('user_id', userId);

      debugPrint('✅ Bot status updated successfully');
      return true;
    } catch (e) {
      debugPrint('❌ Error updating bot status: $e');
      return false;
    }
  }

  /// Archive a trading bot
  Future<bool> archiveBot(String botId) async {
    try {
      final userId = _authService.userId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      debugPrint('🗂️ Archiving bot: $botId');

      await _supabase
          .from('trading_bots')
          .update({
            'is_archived': true,
            'is_active': false,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', botId)
          .eq('user_id', userId);

      debugPrint('✅ Bot archived successfully');
      return true;
    } catch (e) {
      debugPrint('❌ Error archiving bot: $e');
      return false;
    }
  }

  /// Delete a trading bot permanently
  Future<bool> deleteBot(String botId) async {
    try {
      final userId = _authService.userId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      debugPrint('🗑️ Deleting bot: $botId');

      await _supabase
          .from('trading_bots')
          .delete()
          .eq('id', botId)
          .eq('user_id', userId);

      debugPrint('✅ Bot deleted successfully');
      return true;
    } catch (e) {
      debugPrint('❌ Error deleting bot: $e');
      return false;
    }
  }
}
