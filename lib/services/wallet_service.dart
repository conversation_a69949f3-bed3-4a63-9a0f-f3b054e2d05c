import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:crypto/crypto.dart';
import '../config/environment.dart';
import '../models/portfolio_data.dart';
import '../models/transaction.dart';

// Wallet model for Supabase
class Wallet {
  final String id;
  final String userId;
  final String name;
  final String type;
  final String? address;
  final String? encryptedPrivateKey;
  final double balance;
  bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  Wallet({
    required this.id,
    required this.userId,
    required this.name,
    required this.type,
    this.address,
    this.encryptedPrivateKey,
    required this.balance,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Wallet.fromJson(Map<String, dynamic> json) {
    return Wallet(
      id: json['id'],
      userId: json['user_id'],
      name: json['name'],
      type: json['type'],
      address: json['address'],
      encryptedPrivateKey: json['encrypted_private_key'],
      balance: (json['balance'] ?? 0).toDouble(),
      isActive: json['is_active'] ?? false,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'name': name,
      'type': type,
      'address': address,
      'encrypted_private_key': encryptedPrivateKey,
      'balance': balance,
      'is_active': isActive,
    };
  }

  // Getters for compatibility
  String get typeDisplayName {
    switch (type) {
      case 'paper':
        return 'Paper Wallet';
      case 'solana':
        return 'Solana Wallet';
      case 'external':
        return 'External Wallet';
      default:
        return 'Unknown Wallet';
    }
  }

  String get displayAddress {
    if (address == null || address!.isEmpty) return 'No address';
    if (address!.length <= 12) return address!;
    return '${address!.substring(0, 6)}...${address!.substring(address!.length - 6)}';
  }
}

// Paper wallet data
class PaperWallet {
  final String address;
  final String privateKey;
  final String publicKey;
  final String mnemonic;

  PaperWallet({
    required this.address,
    required this.privateKey,
    required this.publicKey,
    required this.mnemonic,
  });
}

class WalletService extends ChangeNotifier {
  static final WalletService _instance = WalletService._internal();
  factory WalletService() => _instance;
  WalletService._internal() {
    _initializeServiceRoleClient();
  }

  final SupabaseClient _supabase = Supabase.instance.client;
  late final SupabaseClient _serviceRoleClient;
  
  List<Wallet> _wallets = [];
  Wallet? _activeWallet;

  List<Wallet> get wallets => List.unmodifiable(_wallets);
  Wallet? get activeWallet => _activeWallet;

  // Initialize service role client for bypassing RLS
  void _initializeServiceRoleClient() {
    _serviceRoleClient = SupabaseClient(
      Environment.supabaseUrl,
      Environment.supabaseServiceRoleKey,
    );
    debugPrint('🔑 Wallet Service role client initialized');
  }

  // Initialize service and load saved wallets
  Future<void> initialize() async {
    await loadUserWallets();
  }

  // Generate a paper wallet
  Future<PaperWallet> generatePaperWallet() async {
    try {
      debugPrint('🔐 Generating paper wallet...');
      
      // Generate random private key (32 bytes)
      final random = Random.secure();
      final privateKeyBytes = List<int>.generate(32, (i) => random.nextInt(256));
      final privateKey = privateKeyBytes.map((b) => b.toRadixString(16).padLeft(2, '0')).join();
      
      // Generate public key (simplified - in real implementation, use proper crypto library)
      final publicKeyBytes = sha256.convert(privateKeyBytes).bytes;
      final publicKey = publicKeyBytes.map((b) => b.toRadixString(16).padLeft(2, '0')).join();
      
      // Generate address (simplified - first 20 bytes of public key hash)
      final addressBytes = sha256.convert(publicKeyBytes).bytes.take(20).toList();
      final address = '0x${addressBytes.map((b) => b.toRadixString(16).padLeft(2, '0')).join()}';
      
      // Generate mnemonic (simplified - 12 words)
      final mnemonic = _generateMnemonic();
      
      debugPrint('✅ Paper wallet generated successfully');
      
      return PaperWallet(
        address: address,
        privateKey: privateKey,
        publicKey: publicKey,
        mnemonic: mnemonic,
      );
    } catch (e) {
      debugPrint('❌ Error generating paper wallet: $e');
      rethrow;
    }
  }

  // Create a new paper wallet
  Future<Wallet> createPaperWallet({
    required String name,
    required String password,
    double? balance,
  }) async {
    try {
      // Generate paper wallet
      final paperWallet = await generatePaperWallet();
      
      // Create wallet in database
      final wallet = await createWallet(
        name: name,
        type: 'paper',
        address: paperWallet.address,
        privateKey: paperWallet.privateKey,
        password: password,
        balance: balance,
      );

      // Add to local list
      _wallets.add(wallet);
      if (_wallets.length == 1) {
        _activeWallet = wallet;
      }

      notifyListeners();
      return wallet;
    } catch (e) {
      debugPrint('❌ Error creating paper wallet: $e');
      rethrow;
    }
  }

  // Generate mnemonic phrase
  String _generateMnemonic() {
    final words = [
      'abandon', 'ability', 'able', 'about', 'above', 'absent', 'absorb', 'abstract',
      'absurd', 'abuse', 'access', 'accident', 'account', 'accuse', 'achieve', 'acid',
      'acoustic', 'acquire', 'across', 'act', 'action', 'actor', 'actress', 'actual',
      'adapt', 'add', 'addict', 'address', 'adjust', 'admit', 'adult', 'advance',
      'advice', 'aerobic', 'affair', 'afford', 'afraid', 'again', 'against', 'age',
      'agent', 'agree', 'ahead', 'aim', 'air', 'airport', 'aisle', 'alarm',
      'album', 'alcohol', 'alert', 'alien', 'all', 'alley', 'allow', 'almost',
      'alone', 'alpha', 'already', 'also', 'alter', 'always', 'amateur', 'amazing',
      'among', 'amount', 'amused', 'analyst', 'anchor', 'ancient', 'anger', 'angle',
      'angry', 'animal', 'ankle', 'announce', 'annual', 'another', 'answer', 'antenna',
      'antique', 'anxiety', 'any', 'apart', 'apology', 'appear', 'apple', 'approve',
      'april', 'arch', 'arctic', 'area', 'arena', 'argue', 'arm', 'armed',
      'armor', 'army', 'around', 'arrange', 'arrest', 'arrive', 'arrow', 'art',
      'artefact', 'artist', 'artwork', 'ask', 'aspect', 'assault', 'asset', 'assist',
      'assume', 'asthma', 'athlete', 'atom', 'attack', 'attend', 'attitude', 'attract',
      'auction', 'audit', 'august', 'aunt', 'author', 'auto', 'autumn', 'average',
      'avocado', 'avoid', 'awake', 'aware', 'away', 'awesome', 'awful', 'awkward'
    ];
    
    final random = Random.secure();
    final selectedWords = <String>[];
    
    for (int i = 0; i < 12; i++) {
      selectedWords.add(words[random.nextInt(words.length)]);
    }
    
    return selectedWords.join(' ');
  }

  // Encrypt private key
  String _encryptPrivateKey(String privateKey, String password) {
    // Simple encryption (in production, use proper encryption library)
    final bytes = utf8.encode(privateKey + password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  // Create wallet in database
  Future<Wallet> createWallet({
    required String name,
    required String type,
    String? address,
    String? privateKey,
    String? password,
    double? balance,
  }) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) throw Exception('User not authenticated');

      debugPrint('💾 Creating wallet in database: $name');

      String? encryptedPrivateKey;
      if (privateKey != null && password != null) {
        encryptedPrivateKey = _encryptPrivateKey(privateKey, password);
      }

      final walletData = {
        'user_id': user.id,
        'name': name,
        'type': type,
        'address': address,
        'encrypted_private_key': encryptedPrivateKey,
        'balance': balance ?? (type == 'paper' ? 1000.0 : 0.0), // Use provided balance or default
        'is_active': _wallets.isEmpty, // First wallet is active
      };

      final response = await _serviceRoleClient
          .from('wallets')
          .insert(walletData)
          .select()
          .single();

      final wallet = Wallet.fromJson(response);
      debugPrint('✅ Wallet created successfully: ${wallet.id}');
      
      return wallet;
    } catch (e) {
      debugPrint('❌ Error creating wallet: $e');
      rethrow;
    }
  }

  // Load user wallets from database
  Future<void> loadUserWallets() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return;

      debugPrint('📱 Loading user wallets...');

      final response = await _serviceRoleClient
          .from('wallets')
          .select()
          .eq('user_id', user.id)
          .order('created_at', ascending: false);

      _wallets = (response as List<dynamic>)
          .map((json) => Wallet.fromJson(json))
          .toList();

      // Set active wallet
      _activeWallet = _wallets.where((w) => w.isActive).firstOrNull;

      debugPrint('✅ Loaded ${_wallets.length} wallets');
      notifyListeners();
    } catch (e) {
      debugPrint('❌ Error loading wallets: $e');
    }
  }

  // Set active wallet
  Future<void> setActiveWallet(String walletId) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) throw Exception('User not authenticated');

      // First, deactivate all wallets
      await _serviceRoleClient
          .from('wallets')
          .update({'is_active': false})
          .eq('user_id', user.id);

      // Then activate the selected wallet
      await _serviceRoleClient
          .from('wallets')
          .update({'is_active': true})
          .eq('id', walletId)
          .eq('user_id', user.id);

      // Update local state
      for (var wallet in _wallets) {
        wallet.isActive = wallet.id == walletId;
        if (wallet.id == walletId) {
          _activeWallet = wallet;
        }
      }

      debugPrint('✅ Active wallet set: $walletId');
      notifyListeners();
    } catch (e) {
      debugPrint('❌ Error setting active wallet: $e');
    }
  }

  // Delete wallet
  Future<bool> deleteWallet(String walletId) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) throw Exception('User not authenticated');

      await _serviceRoleClient
          .from('wallets')
          .delete()
          .eq('id', walletId)
          .eq('user_id', user.id);

      // Remove from local list
      _wallets.removeWhere((w) => w.id == walletId);

      // If deleted wallet was active, set first wallet as active
      if (_activeWallet?.id == walletId) {
        _activeWallet = _wallets.isNotEmpty ? _wallets.first : null;
        if (_activeWallet != null) {
          await setActiveWallet(_activeWallet!.id);
        }
      }

      debugPrint('✅ Wallet deleted successfully');
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('❌ Error deleting wallet: $e');
      return false;
    }
  }

  // Update wallet balance
  Future<void> updateWalletBalance(String walletId, double newBalance) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) throw Exception('User not authenticated');

      await _serviceRoleClient
          .from('wallets')
          .update({'balance': newBalance})
          .eq('id', walletId)
          .eq('user_id', user.id);

      // Update local state
      final walletIndex = _wallets.indexWhere((w) => w.id == walletId);
      if (walletIndex != -1) {
        // Create new wallet instance with updated balance
        final oldWallet = _wallets[walletIndex];
        final updatedWallet = Wallet(
          id: oldWallet.id,
          userId: oldWallet.userId,
          name: oldWallet.name,
          type: oldWallet.type,
          address: oldWallet.address,
          encryptedPrivateKey: oldWallet.encryptedPrivateKey,
          balance: newBalance,
          isActive: oldWallet.isActive,
          createdAt: oldWallet.createdAt,
          updatedAt: DateTime.now(),
        );

        _wallets[walletIndex] = updatedWallet;

        if (_activeWallet?.id == walletId) {
          _activeWallet = updatedWallet;
        }
      }

      debugPrint('✅ Wallet balance updated: $walletId -> $newBalance');
      notifyListeners();
    } catch (e) {
      debugPrint('❌ Error updating wallet balance: $e');
    }
  }

  // Get wallet by ID
  Wallet? getWalletById(String walletId) {
    try {
      return _wallets.firstWhere((w) => w.id == walletId);
    } catch (e) {
      return null;
    }
  }

  // Get total balance across all wallets
  double get totalBalance {
    return _wallets.fold(0.0, (sum, wallet) => sum + wallet.balance);
  }

  // Check if user has any wallets
  bool get hasWallets => _wallets.isNotEmpty;

  // Get wallets by type
  List<Wallet> getWalletsByType(String type) {
    return _wallets.where((w) => w.type == type).toList();
  }

  // Get portfolio data (mock implementation for now)
  Future<PortfolioData> getPortfolioData() async {
    // This would normally fetch real portfolio data from blockchain
    // For now, return mock data based on wallet balances
    final totalValue = _wallets.fold(0.0, (sum, wallet) => sum + wallet.balance);

    return PortfolioData(
      totalValue: totalValue,
      totalChange: totalValue * 0.05, // Mock 5% change
      totalChangePercent: 5.0,
      holdings: _wallets.map((wallet) => TokenHolding(
        symbol: 'SOL',
        name: 'Solana',
        amount: wallet.balance,
        value: wallet.balance * 100, // Mock price
        change: wallet.balance * 5, // Mock change
        changePercent: 5.0,
        imageUrl: 'https://cryptologos.cc/logos/solana-sol-logo.png',
        balance: wallet.balance,
      )).toList(),
    );
  }

  // Get wallet transactions (mock implementation for now)
  Future<List<Transaction>> getWalletTransactions(Wallet wallet) async {
    // This would normally fetch real transactions from blockchain
    // For now, return mock transactions
    return [
      Transaction(
        id: '1',
        type: TransactionType.receive,
        status: TransactionStatus.confirmed,
        amount: 1.0,
        symbol: 'SOL',
        timestamp: DateTime.now().subtract(const Duration(hours: 1)),
        hash: '0x123...abc',
        token: 'SOL',
      ),
      Transaction(
        id: '2',
        type: TransactionType.send,
        status: TransactionStatus.confirmed,
        amount: 0.5,
        symbol: 'SOL',
        timestamp: DateTime.now().subtract(const Duration(hours: 2)),
        hash: '0x456...def',
        token: 'SOL',
      ),
    ];
  }

  // Create Solana wallet (for compatibility)
  Future<Wallet> createSolanaWallet({required String name}) async {
    return await createWallet(name: name, type: 'solana');
  }
}
