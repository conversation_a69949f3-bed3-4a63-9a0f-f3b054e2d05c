/// Utility class for formatting currency values with appropriate suffixes
class CurrencyFormatter {
  /// Formats a currency value with K, M, B suffixes and $ symbol
  /// 
  /// Examples:
  /// - 1234.56 → $1.23K
  /// - 1234567.89 → $1.23M
  /// - 1234567890.12 → $1.23B
  /// - 45.90 → $45.90
  static String format(double? value) {
    if (value == null || value == 0) return '\$0.00';
    
    if (value >= 1000000000) {
      return '\$${(value / 1000000000).toStringAsFixed(2)}B';
    } else if (value >= 1000000) {
      return '\$${(value / 1000000).toStringAsFixed(2)}M';
    } else if (value >= 1000) {
      return '\$${(value / 1000).toStringAsFixed(2)}K';
    } else {
      return '\$${value.toStringAsFixed(2)}';
    }
  }

  /// Formats a currency value without suffixes (always shows full amount with $ symbol)
  /// 
  /// Examples:
  /// - 1234.56 → $1,234.56
  /// - 1234567.89 → $1,234,567.89
  static String formatFull(double? value) {
    if (value == null || value == 0) return '\$0.00';
    
    // Format with commas for thousands separators
    final formatter = RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))');
    String result = value.toStringAsFixed(2);
    result = result.replaceAllMapped(formatter, (Match m) => '${m[1]},');
    
    return '\$$result';
  }

  /// Formats a percentage value with + or - sign
  /// 
  /// Examples:
  /// - 5.67 → +5.67%
  /// - -2.34 → -2.34%
  /// - 0 → 0.00%
  static String formatPercentage(double? value) {
    if (value == null) return '0.00%';
    
    final sign = value >= 0 ? '+' : '';
    return '$sign${value.toStringAsFixed(2)}%';
  }
}
