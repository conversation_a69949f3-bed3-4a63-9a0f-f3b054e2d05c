import 'package:flutter/foundation.dart';
import 'dart:html' as html;

class WebTitleHelper {
  static void setTitle(String title) {
    if (kIsWeb) {
      html.document.title = title;
    }
  }

  static void setPageTitle(String pageName) {
    if (kIsWeb) {
      html.document.title = 'DexTrip - $pageName';
    }
  }

  static void setCoinDetailTitle(String coinName) {
    if (kIsWeb) {
      html.document.title = 'DexTrip - $coinName Price & Chart';
    }
  }

  static void setBotDetailTitle(String botName) {
    if (kIsWeb) {
      html.document.title = 'DexTrip - $botName Trading Bot';
    }
  }

  static void setDefaultTitle() {
    if (kIsWeb) {
      html.document.title = 'DexTrip - Crypto Trading Platform';
    }
  }
}