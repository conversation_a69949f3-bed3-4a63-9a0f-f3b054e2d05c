import 'package:flutter/material.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import '../theme/app_theme.dart';
import '../widgets/gradient_button.dart';
import '../widgets/bot_config_modal.dart';
import '../models/trading_bot_model.dart' as old_model;
import '../models/trading_bot.dart';
import '../models/coin_model.dart';
import '../services/trading_bot_service.dart';
import '../utils/toast_utils.dart';

enum BotConfigSection {
  main,
  tradingStrategy,
  indicators,
  riskManagement,
  tradingHours,
}

class CreateBotModal extends StatefulWidget {
  final String coinSymbol;
  final VoidCallback? onBotCreated;
  final bool isEditing;

  const CreateBotModal({
    super.key,
    required this.coinSymbol,
    this.onBotCreated,
    this.isEditing = false,
  });

  @override
  State<CreateBotModal> createState() => _CreateBotModalState();
}

class _CreateBotModalState extends State<CreateBotModal> {
  BotConfigSection _currentSection = BotConfigSection.main;

  // Form controllers
  final _botNameController = TextEditingController();
  final _investmentController = TextEditingController();
  final _stopLossController = TextEditingController();
  final _takeProfitController = TextEditingController();

  // Bot configuration state
  String _selectedStrategy = 'DCA';
  bool _useStopLoss = true;
  bool _useTakeProfit = true;
  double _riskPercentage = 2.0;
  bool _tradingEnabled = true;
  bool _startImmediately = false;
  List<String> _selectedIndicators = [];

  @override
  void initState() {
    super.initState();
    _botNameController.text = '${widget.coinSymbol.toUpperCase()} Bot';
    // Set default values
    _investmentController.text = '100';
    _stopLossController.text = '5';
    _takeProfitController.text = '10';
  }

  @override
  void dispose() {
    _botNameController.dispose();
    _investmentController.dispose();
    _stopLossController.dispose();
    _takeProfitController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.85,

      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          _buildHeader(),

          // Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: _buildCurrentSectionContent(),
            ),
          ),

          // Fixed bottom button
          _buildBottomButton(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Row(
        children: [
          if (_currentSection != BotConfigSection.main)
            IconButton(
              onPressed: () =>
                  setState(() => _currentSection = BotConfigSection.main),
              icon: const Icon(
                LucideIcons.arrowLeft,
                color: Colors.white,
                size: 20,
              ),
            ),

          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              LucideIcons.bot,
              color: AppTheme.primaryColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getSectionTitle(),
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Text(
                  _getSectionSubtitle(),
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.white.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ),

          // Add new bot button for indicators section
          if (_currentSection == BotConfigSection.indicators)
            IconButton(
              onPressed: _showBotConfigModal,
              icon: const Icon(LucideIcons.plus, color: Colors.white, size: 20),
              tooltip: 'Add New Bot',
            ),

          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(LucideIcons.x, color: Colors.white, size: 20),
          ),
        ],
      ),
    );
  }

  String _getSectionTitle() {
    switch (_currentSection) {
      case BotConfigSection.main:
        return widget.isEditing ? 'Edit Trading Bot' : 'Create Trading Bot';
      case BotConfigSection.tradingStrategy:
        return 'Trading Strategy';
      case BotConfigSection.indicators:
        return 'Technical Indicators';
      case BotConfigSection.riskManagement:
        return 'Risk Management';
      case BotConfigSection.tradingHours:
        return 'Trading Hours';
    }
  }

  String _getSectionSubtitle() {
    switch (_currentSection) {
      case BotConfigSection.main:
        return 'Configure bot for ${widget.coinSymbol.toUpperCase()}';
      case BotConfigSection.tradingStrategy:
        return 'Select your trading approach';
      case BotConfigSection.indicators:
        return 'Choose technical analysis tools';
      case BotConfigSection.riskManagement:
        return 'Set risk parameters';
      case BotConfigSection.tradingHours:
        return 'Define active trading periods';
    }
  }

  Widget _buildCurrentSectionContent() {
    switch (_currentSection) {
      case BotConfigSection.main:
        return _buildMainContent();
      case BotConfigSection.tradingStrategy:
        return _buildTradingStrategyContent();
      case BotConfigSection.indicators:
        return _buildIndicatorsContent();
      case BotConfigSection.riskManagement:
        return _buildRiskManagementContent();
      case BotConfigSection.tradingHours:
        return _buildTradingHoursContent();
    }
  }

  Widget _buildMainContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Bot Name
        _buildInputSection(
          'Bot Name',
          'Give your bot a memorable name',
          TextField(
            controller: _botNameController,
            style: const TextStyle(color: Colors.white),
            decoration: InputDecoration(
              hintText: 'Enter bot name',
              hintStyle: TextStyle(color: Colors.white.withValues(alpha: 0.5)),
              filled: true,
              fillColor: Colors.white.withValues(alpha: 0.05),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: Colors.white.withValues(alpha: 0.1),
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: Colors.white.withValues(alpha: 0.1),
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppTheme.primaryColor),
              ),
            ),
          ),
        ),

        const SizedBox(height: 32),

        // Configuration Sections
        _buildConfigSection(
          'Trading Strategy',
          'DCA, Grid Trading, Momentum',
          LucideIcons.trendingUp,
          () => setState(
            () => _currentSection = BotConfigSection.tradingStrategy,
          ),
        ),

        _buildConfigSection(
          'Technical Indicators',
          'RSI, MACD, Bollinger Bands',
          LucideIcons.activity,
          () => setState(() => _currentSection = BotConfigSection.indicators),
        ),

        _buildConfigSection(
          'Risk Management',
          'Stop Loss, Take Profit, Position Size',
          LucideIcons.shield,
          () =>
              setState(() => _currentSection = BotConfigSection.riskManagement),
        ),

        _buildConfigSection(
          'Trading Hours',
          '24/7 or Custom Schedule',
          LucideIcons.clock,
          () => setState(() => _currentSection = BotConfigSection.tradingHours),
        ),

        const SizedBox(height: 32),
      ],
    );
  }

  Widget _buildInputSection(String title, String subtitle, Widget child) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          subtitle,
          style: TextStyle(
            fontSize: 14,
            color: Colors.white.withValues(alpha: 0.6),
          ),
        ),
        const SizedBox(height: 16),
        child,
      ],
    );
  }

  Widget _buildConfigSection(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: AppTheme.primaryColor, size: 20),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.white.withValues(alpha: 0.6),
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  LucideIcons.chevronRight,
                  color: Colors.white.withValues(alpha: 0.4),
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBottomButton() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: const Color(0xFF0F0F0F),
        border: Border(
          top: BorderSide(color: Colors.white.withValues(alpha: 0.1), width: 1),
        ),
      ),
      child: SafeArea(
        child: GradientButton(
          text: _currentSection == BotConfigSection.main
              ? (_isCreating
                    ? 'Creating Bot...'
                    : (widget.isEditing ? 'Update Bot' : 'Start Trading Bot'))
              : 'Save Configuration',
          onPressed: _isCreating ? () {} : _handleButtonPress,
          icon: _currentSection == BotConfigSection.main
              ? (_isCreating ? LucideIcons.loader : LucideIcons.play)
              : LucideIcons.check,
        ),
      ),
    );
  }

  bool _isCreating = false;

  void _handleButtonPress() {
    if (_currentSection == BotConfigSection.main) {
      // Start/Update bot - create in database
      _createTradingBot();
    } else {
      // Save section configuration and go back to main
      setState(() => _currentSection = BotConfigSection.main);
    }
  }

  Future<void> _createTradingBot() async {
    if (_isCreating) return; // Prevent double submission

    // Validate required fields
    if (_botNameController.text.trim().isEmpty) {
      ToastUtils.showError(context, 'Please enter a bot name');
      return;
    }

    if (_investmentController.text.trim().isEmpty) {
      ToastUtils.showError(context, 'Please enter an investment amount');
      return;
    }

    final investmentAmount = double.tryParse(_investmentController.text);
    if (investmentAmount == null || investmentAmount <= 0) {
      ToastUtils.showError(context, 'Please enter a valid investment amount');
      return;
    }

    final stopLoss = double.tryParse(_stopLossController.text) ?? 0.0;
    final takeProfit = double.tryParse(_takeProfitController.text) ?? 0.0;

    setState(() => _isCreating = true);

    try {
      debugPrint('🚀 Starting bot creation process...');
      debugPrint('📝 Form data validation:');
      debugPrint('  Bot name: "${_botNameController.text.trim()}"');
      debugPrint('  Investment: "${_investmentController.text}"');
      debugPrint('  Stop loss: "${_stopLossController.text}"');
      debugPrint('  Take profit: "${_takeProfitController.text}"');
      debugPrint('  Strategy: "$_selectedStrategy"');
      debugPrint('  Start immediately: $_startImmediately');
      debugPrint('  Risk percentage: $_riskPercentage');
      debugPrint('  Trading 24/7: $_tradingEnabled');
      debugPrint('  Selected indicators: $_selectedIndicators');

      // Test database connection and schema first
      final dbTest = await TradingBotService().testDatabaseConnection();
      if (!dbTest) {
        throw Exception(
          'Database connection or schema validation failed. Please check the database setup.',
        );
      }

      // Create coin data from the symbol
      final coinData = CoinData(
        id: widget.coinSymbol.toLowerCase(),
        symbol: widget.coinSymbol.toUpperCase(),
        name: widget.coinSymbol.toUpperCase(),
        image: null, // Will be populated from actual coin data if available
      );

      debugPrint('💰 Coin data: ${coinData.toJson()}');

      // Convert selected indicators to TradingIndicator objects
      final indicators = _selectedIndicators.map((indicatorName) {
        final indicator = TradingIndicator(
          type: _getIndicatorType(indicatorName),
          parameters: _getDefaultParameters(indicatorName),
          buyThreshold: _getDefaultBuyThreshold(indicatorName),
          sellThreshold: _getDefaultSellThreshold(indicatorName),
        );
        debugPrint(
          '📊 Created indicator: $indicatorName -> ${indicator.toJson()}',
        );
        return indicator;
      }).toList();

      debugPrint('📈 Total indicators created: ${indicators.length}');

      // Create trading bot in database
      final result = await TradingBotService().createTradingBot(
        name: _botNameController.text.trim(),
        coin: coinData,
        strategy: _selectedStrategy,
        investmentAmount: investmentAmount,
        stopLoss: stopLoss,
        takeProfit: takeProfit,
        indicators: indicators,
        startImmediately: _startImmediately,
        riskPercentage: _riskPercentage,
        trading24_7: _tradingEnabled,
        tradingStartTime: _tradingEnabled ? null : '09:00',
        tradingEndTime: _tradingEnabled ? null : '17:00',
      );

      if (result != null) {
        if (mounted) {
          Navigator.pop(context);
          widget.onBotCreated?.call();
          _showBotCreatedSnackbar();
        }
      }
    } catch (e) {
      if (mounted) {
        ToastUtils.showError(
          context,
          'Failed to create trading bot: ${e.toString()}',
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isCreating = false);
      }
    }
  }

  // Helper methods for indicator configuration
  IndicatorType _getIndicatorType(String indicatorName) {
    switch (indicatorName) {
      case 'RSI':
        return IndicatorType.rsi;
      case 'MACD':
        return IndicatorType.macd;
      case 'SMA':
        return IndicatorType.sma;
      case 'EMA':
        return IndicatorType.ema;
      case 'Bollinger Bands':
        return IndicatorType.bollinger;
      case 'Stochastic':
        return IndicatorType.stochastic;
      case 'Williams %R':
        return IndicatorType.williams;
      case 'ADX':
        return IndicatorType.adx;
      default:
        return IndicatorType.rsi;
    }
  }

  Map<String, dynamic> _getDefaultParameters(String indicatorName) {
    switch (indicatorName) {
      case 'RSI':
        return {'period': 14};
      case 'MACD':
        return {'fastPeriod': 12, 'slowPeriod': 26, 'signalPeriod': 9};
      case 'SMA':
        return {'period': 20};
      case 'EMA':
        return {'period': 20};
      case 'Bollinger Bands':
        return {'period': 20, 'standardDeviations': 2};
      case 'Stochastic':
        return {'kPeriod': 14, 'dPeriod': 3};
      case 'Williams %R':
        return {'period': 14};
      case 'ADX':
        return {'period': 14};
      default:
        return {'period': 14};
    }
  }

  double _getDefaultBuyThreshold(String indicatorName) {
    switch (indicatorName) {
      case 'RSI':
        return 30.0;
      case 'MACD':
        return 0.0;
      case 'Stochastic':
        return 20.0;
      case 'Williams %R':
        return -80.0;
      case 'ADX':
        return 25.0;
      default:
        return 30.0;
    }
  }

  double _getDefaultSellThreshold(String indicatorName) {
    switch (indicatorName) {
      case 'RSI':
        return 70.0;
      case 'MACD':
        return 0.0;
      case 'Stochastic':
        return 80.0;
      case 'Williams %R':
        return -20.0;
      case 'ADX':
        return 25.0;
      default:
        return 70.0;
    }
  }

  void _showBotCreatedSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(LucideIcons.check, color: Colors.green, size: 20),
            const SizedBox(width: 12),
            Text(
              widget.isEditing
                  ? 'Bot updated successfully'
                  : 'Trading bot started for ${widget.coinSymbol.toUpperCase()}',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        backgroundColor: Colors.green.withValues(alpha: 0.9),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        duration: const Duration(seconds: 3),
      ),
    );
  }

  Widget _buildTradingStrategyContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildStrategyOption(
          'DCA (Dollar Cost Averaging)',
          'Invest fixed amounts at regular intervals',
          _selectedStrategy == 'DCA',
          () => setState(() => _selectedStrategy = 'DCA'),
        ),
        _buildStrategyOption(
          'Grid Trading',
          'Place buy and sell orders at regular intervals',
          _selectedStrategy == 'Grid',
          () => setState(() => _selectedStrategy = 'Grid'),
        ),
        _buildStrategyOption(
          'Momentum Trading',
          'Follow price trends and momentum',
          _selectedStrategy == 'Momentum',
          () => setState(() => _selectedStrategy = 'Momentum'),
        ),
        _buildStrategyOption(
          'Mean Reversion',
          'Buy low, sell high based on averages',
          _selectedStrategy == 'MeanReversion',
          () => setState(() => _selectedStrategy = 'MeanReversion'),
        ),
        const SizedBox(height: 32),
      ],
    );
  }

  Widget _buildIndicatorsContent() {
    final availableIndicators = [
      {
        'name': 'RSI',
        'description': 'Relative Strength Index - Momentum oscillator',
      },
      {'name': 'MACD', 'description': 'Moving Average Convergence Divergence'},
      {'name': 'SMA', 'description': 'Simple Moving Average'},
      {'name': 'EMA', 'description': 'Exponential Moving Average'},
      {'name': 'Bollinger Bands', 'description': 'Volatility indicator'},
      {'name': 'Stochastic', 'description': 'Stochastic Oscillator'},
      {'name': 'Williams %R', 'description': 'Momentum indicator'},
      {
        'name': 'ADX',
        'description': 'Average Directional Index - Trend strength',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select Technical Indicators',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Choose indicators to guide your trading decisions',
          style: TextStyle(
            fontSize: 14,
            color: Colors.white.withValues(alpha: 0.6),
          ),
        ),
        const SizedBox(height: 24),

        ...availableIndicators.map((indicator) {
          final isSelected = _selectedIndicators.contains(indicator['name']);
          return Container(
            margin: const EdgeInsets.only(bottom: 12),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  setState(() {
                    if (isSelected) {
                      _selectedIndicators.remove(indicator['name']);
                    } else {
                      _selectedIndicators.add(indicator['name']!);
                    }
                  });
                },
                borderRadius: BorderRadius.circular(12),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? AppTheme.primaryColor.withValues(alpha: 0.1)
                        : Colors.white.withValues(alpha: 0.05),
                    border: Border.all(
                      color: isSelected
                          ? AppTheme.primaryColor
                          : Colors.white.withValues(alpha: 0.1),
                      width: 1,
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        isSelected ? LucideIcons.check : LucideIcons.circle,
                        color: isSelected
                            ? AppTheme.primaryColor
                            : Colors.white.withValues(alpha: 0.6),
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              indicator['name']!,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: isSelected
                                    ? AppTheme.primaryColor
                                    : Colors.white,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              indicator['description']!,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.white.withValues(alpha: 0.6),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        }).toList(),

        const SizedBox(height: 32),
      ],
    );
  }

  void _showBotConfigModal() {
    // Create a dummy bot for configuration using old model
    final dummyBot = old_model.TradingBot(
      id: 'new_bot',
      name: '${widget.coinSymbol} New Bot',
      tokenSymbol: widget.coinSymbol,
      tokenImage: '',
      currentPrice: 0.0,
      priceChange24h: 0.0,
      isActive: false,
      strategy: 'DCA',
      solPerTrade: 0.1,
      totalTrades: 0,
      winRate: 0.0,
      totalEarned: 0.0,
      trades: [],
    );

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => BotConfigModal(bot: dummyBot, isCreating: true),
    );
  }

  Widget _buildRiskManagementContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Investment Amount
        _buildInputSection(
          'Investment Amount',
          'Amount to invest in USDT',
          TextField(
            controller: _investmentController,
            style: const TextStyle(color: Colors.white),
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              hintText: 'Enter investment amount',
              hintStyle: TextStyle(color: Colors.white.withValues(alpha: 0.5)),
              prefixText: '\$ ',
              prefixStyle: const TextStyle(color: Colors.white),
              filled: true,
              fillColor: Colors.white.withValues(alpha: 0.05),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: Colors.white.withValues(alpha: 0.1),
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: Colors.white.withValues(alpha: 0.1),
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppTheme.primaryColor),
              ),
            ),
          ),
        ),
        const SizedBox(height: 24),

        _buildRiskToggle(
          'Stop Loss',
          'Automatically sell to limit losses',
          _useStopLoss,
          (value) => setState(() => _useStopLoss = value),
        ),
        if (_useStopLoss) ...[
          const SizedBox(height: 16),
          _buildInputSection(
            'Stop Loss Percentage',
            'Percentage loss to trigger stop loss',
            TextField(
              controller: _stopLossController,
              style: const TextStyle(color: Colors.white),
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                hintText: 'Enter stop loss %',
                hintStyle: TextStyle(
                  color: Colors.white.withValues(alpha: 0.5),
                ),
                suffixText: '%',
                suffixStyle: const TextStyle(color: Colors.white),
                filled: true,
                fillColor: Colors.white.withValues(alpha: 0.05),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Colors.white.withValues(alpha: 0.1),
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Colors.white.withValues(alpha: 0.1),
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: AppTheme.primaryColor),
                ),
              ),
            ),
          ),
        ],

        _buildRiskToggle(
          'Take Profit',
          'Automatically sell to secure profits',
          _useTakeProfit,
          (value) => setState(() => _useTakeProfit = value),
        ),
        if (_useTakeProfit) ...[
          const SizedBox(height: 16),
          _buildInputSection(
            'Take Profit Percentage',
            'Percentage gain to trigger take profit',
            TextField(
              controller: _takeProfitController,
              style: const TextStyle(color: Colors.white),
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                hintText: 'Enter take profit %',
                hintStyle: TextStyle(
                  color: Colors.white.withValues(alpha: 0.5),
                ),
                suffixText: '%',
                suffixStyle: const TextStyle(color: Colors.white),
                filled: true,
                fillColor: Colors.white.withValues(alpha: 0.05),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Colors.white.withValues(alpha: 0.1),
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Colors.white.withValues(alpha: 0.1),
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: AppTheme.primaryColor),
                ),
              ),
            ),
          ),
        ],

        const SizedBox(height: 24),
        _buildRiskSlider(
          'Risk Per Trade',
          'Percentage of portfolio to risk per trade',
          _riskPercentage,
          1.0,
          10.0,
          (value) => setState(() => _riskPercentage = value),
        ),

        const SizedBox(height: 24),
        _buildRiskToggle(
          'Start Immediately',
          'Start trading as soon as the bot is created',
          _startImmediately,
          (value) => setState(() => _startImmediately = value),
        ),

        const SizedBox(height: 32),
      ],
    );
  }

  Widget _buildTradingHoursContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTradingToggle(
          '24/7 Trading',
          'Trade continuously without breaks',
          _tradingEnabled,
          (value) => setState(() => _tradingEnabled = value),
        ),
        const SizedBox(height: 24),
        if (!_tradingEnabled) ...[
          Text(
            'Custom Trading Hours',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          _buildTimeSelector('Start Time', '09:00'),
          const SizedBox(height: 12),
          _buildTimeSelector('End Time', '17:00'),
        ],
        const SizedBox(height: 32),
      ],
    );
  }

  Widget _buildStrategyOption(
    String title,
    String description,
    bool isSelected,
    VoidCallback onTap,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: isSelected
                  ? AppTheme.primaryColor.withValues(alpha: 0.1)
                  : Colors.white.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isSelected
                    ? AppTheme.primaryColor
                    : Colors.white.withValues(alpha: 0.1),
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isSelected
                          ? AppTheme.primaryColor
                          : Colors.white.withValues(alpha: 0.4),
                      width: 2,
                    ),
                  ),
                  child: isSelected
                      ? Center(
                          child: Container(
                            width: 10,
                            height: 10,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: AppTheme.primaryColor,
                            ),
                          ),
                        )
                      : null,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        description,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.white.withValues(alpha: 0.6),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildIndicatorToggle(
    String title,
    String description,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
      ),
      child: Row(
        children: [
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppTheme.primaryColor,
            activeTrackColor: AppTheme.primaryColor.withValues(alpha: 0.3),
            inactiveThumbColor: Colors.white.withValues(alpha: 0.6),
            inactiveTrackColor: Colors.white.withValues(alpha: 0.1),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.white.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRiskToggle(
    String title,
    String description,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return _buildIndicatorToggle(title, description, value, onChanged);
  }

  Widget _buildTradingToggle(
    String title,
    String description,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return _buildIndicatorToggle(title, description, value, onChanged);
  }

  Widget _buildRiskSlider(
    String title,
    String description,
    double value,
    double min,
    double max,
    ValueChanged<double> onChanged,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.white.withValues(alpha: 0.6),
                    ),
                  ),
                ],
              ),
              Text(
                '${value.toStringAsFixed(1)}%',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: AppTheme.primaryColor,
              inactiveTrackColor: Colors.white.withValues(alpha: 0.2),
              thumbColor: AppTheme.primaryColor,
              overlayColor: AppTheme.primaryColor.withValues(alpha: 0.2),
              trackHeight: 4,
            ),
            child: Slider(
              value: value,
              min: min,
              max: max,
              divisions: ((max - min) * 2).toInt(),
              onChanged: onChanged,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeSelector(String label, String time) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          Row(
            children: [
              Text(
                time,
                style: TextStyle(
                  fontSize: 16,
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(width: 8),
              Icon(
                LucideIcons.clock,
                color: Colors.white.withValues(alpha: 0.4),
                size: 16,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
