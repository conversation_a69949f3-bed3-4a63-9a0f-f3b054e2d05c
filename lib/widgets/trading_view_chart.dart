
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

class TradingViewChart extends StatefulWidget {
  final String symbol;
  final String interval;
  final String tokenAddress;

  const TradingViewChart({
    Key? key,
    required this.symbol,
    required this.interval,
    required this.tokenAddress
  }) : super(key: key);

  @override
  State<TradingViewChart> createState() => _TradingViewChartState();
}

class _TradingViewChartState extends State<TradingViewChart> {
  late final WebViewController _controller;
String _buildHtml(String symbol, String interval, int style) {
  return '''
<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script type="text/javascript" src="https://s3.tradingview.com/tv.js"></script>
    <style>
      html, body {
        margin: 0;
        padding: 0;
        height: 100%;
        background-color: transparent;
      }
    </style>
  </head>
  <body>
    <div id="chart" style="height: 100%;"></div>
    <script type="text/javascript">
      let widget = null;

      function loadWidget(symbol, interval, style) {
        document.getElementById("chart").innerHTML = "";
        widget = new TradingView.widget({
          "container_id": "chart",
          "width": "100%",
          "height": "100%",
          "symbol": symbol,
          "interval": interval,
          "timezone": "Etc/UTC",
          "theme": "dark",
          "style": style,
          "locale": "en",
          "toolbar_bg": "#000",
          "hide_top_toolbar": true,
          "enable_publishing": false,
          "allow_symbol_change": false
        });
      }

      window.onload = function () {
        loadWidget("$symbol", "$interval", $style);
      };
    </script>
  </body>
</html>
''';
}
  @override
  void initState() {
    super.initState();
    final html = _buildHtml(widget.symbol, widget.interval,1);
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..loadHtmlString(html);
  }

  @override
  void didUpdateWidget(covariant TradingViewChart oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.symbol != widget.symbol || oldWidget.interval != widget.interval) {
      final js = 'loadWidget("${widget.symbol}", "${widget.interval}");';
      _controller.runJavaScript(js);
    }
  }

  @override
  Widget build(BuildContext context) {
    return WebViewWidget(controller: _controller);
  }
}